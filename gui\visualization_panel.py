"""
可视化面板模块
用于显示优化过程和结果的可视化图表
"""

import os
import sys
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from matplotlib.figure import Figure

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QComboBox, 
                            QPushButton, QLabel, QTabWidget, QSplitter, QFrame,
                            QGroupBox, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot


class MplCanvas(FigureCanvas):
    """用于Matplotlib图形的画布类"""
    
    def __init__(self, width=10, height=8, dpi=100):
        """初始化画布
        
        Args:
            width (float): 宽度（英寸）
            height (float): 高度（英寸）
            dpi (int): 分辨率（每英寸点数）
        """
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        
        # 调整布局，避免裁剪
        self.fig.tight_layout()
        
        # 初始化父类
        super(MplCanvas, self).__init__(self.fig)


class VisualizationPanel(QWidget):
    """可视化面板
    
    提供用于显示优化过程和结果的可视化功能
    """
    
    def __init__(self, parent=None):
        """初始化可视化面板
        
        Args:
            parent (QWidget, optional): 父窗口部件
        """
        super(VisualizationPanel, self).__init__(parent)
        
        # 设置布局
        self.init_ui()
        
        # 初始化结构列表
        self.structures = []
    
    def init_ui(self):
        """初始化UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建选项卡部件
        self.tab_widget = QTabWidget()
        
        # 创建优化进程选项卡
        self.optimization_tab = QWidget()
        opt_layout = QVBoxLayout(self.optimization_tab)
        
        # 创建画布
        self.optimization_canvas = MplCanvas(width=8, height=6, dpi=100)
        self.optimization_toolbar = NavigationToolbar(self.optimization_canvas, self)
        
        # 添加到布局
        opt_layout.addWidget(self.optimization_toolbar)
        opt_layout.addWidget(self.optimization_canvas)
        
        # 创建参数空间选项卡
        self.parameter_space_tab = QWidget()
        param_layout = QVBoxLayout(self.parameter_space_tab)
        
        # 创建画布
        self.parameter_space_canvas = MplCanvas(width=8, height=6, dpi=100)
        self.parameter_space_toolbar = NavigationToolbar(self.parameter_space_canvas, self)
        
        # 添加到布局
        param_layout.addWidget(self.parameter_space_toolbar)
        param_layout.addWidget(self.parameter_space_canvas)
        
        # 创建帕累托前沿选项卡
        self.pareto_tab = QWidget()
        pareto_layout = QVBoxLayout(self.pareto_tab)
        
        # 创建画布
        self.pareto_canvas = MplCanvas(width=8, height=6, dpi=100)
        self.pareto_toolbar = NavigationToolbar(self.pareto_canvas, self)
        
        # 添加到布局
        pareto_layout.addWidget(self.pareto_toolbar)
        pareto_layout.addWidget(self.pareto_canvas)
        
        # 添加选项卡
        self.tab_widget.addTab(self.optimization_tab, "优化进程")
        self.tab_widget.addTab(self.parameter_space_tab, "参数空间")
        self.tab_widget.addTab(self.pareto_tab, "帕累托前沿")
        
        # 添加到主布局
        main_layout.addWidget(self.tab_widget)
        
        # 控制区域
        control_layout = QHBoxLayout()
        
        # 图表类型选择
        self.chart_type_label = QLabel("图表类型:")
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["能量误差", "力误差", "综合误差"])
        
        # 导出按钮
        self.export_button = QPushButton("导出图表")
        self.export_button.clicked.connect(self.export_current_figure)
        
        # 添加到控制布局
        control_layout.addWidget(self.chart_type_label)
        control_layout.addWidget(self.chart_type_combo)
        control_layout.addStretch()
        control_layout.addWidget(self.export_button)
        
        # 添加控制区域到主布局
        main_layout.addLayout(control_layout)
    
    def update_optimization_plot(self, iterations, values):
        """更新优化过程图表
        
        Args:
            iterations (array): 迭代次数数组
            values (array): 目标函数值数组
        """
        # 清除当前图形
        self.optimization_canvas.axes.clear()
        
        # 绘制新图形
        self.optimization_canvas.axes.plot(iterations, values, 'b-')
        self.optimization_canvas.axes.set_xlabel('迭代次数')
        self.optimization_canvas.axes.set_ylabel('目标函数值')
        self.optimization_canvas.axes.set_title('优化过程')
        self.optimization_canvas.axes.grid(True)
        
        # 重绘
        self.optimization_canvas.fig.tight_layout()
        self.optimization_canvas.draw()
        
        # 切换到优化进程选项卡
        self.tab_widget.setCurrentWidget(self.optimization_tab)
    
    def update_optimization_results(self, results):
        """更新优化结果可视化
        
        Args:
            results (list): 优化结果列表
        """
        # 在实际应用中，这里应有完整的结果可视化逻辑
        pass
    
    def display_figure(self, fig):
        """显示外部创建的图形
        
        Args:
            fig (matplotlib.figure.Figure): 要显示的图形
        """
        # 清除当前图形
        self.pareto_canvas.axes.clear()
        
        # 复制图形内容
        for ax in fig.axes:
            # 获取图形数据
            lines = ax.get_lines()
            scatter_plots = ax.collections
            
            # 复制线条
            for line in lines:
                x, y = line.get_data()
                self.pareto_canvas.axes.plot(x, y, 
                                          color=line.get_color(),
                                          linestyle=line.get_linestyle(),
                                          marker=line.get_marker(),
                                          label=line.get_label())
            
            # 复制散点图
            for scatter in scatter_plots:
                if hasattr(scatter, 'get_offsets'):
                    offsets = scatter.get_offsets()
                    if len(offsets) > 0:
                        x = offsets[:, 0]
                        y = offsets[:, 1]
                        self.pareto_canvas.axes.scatter(x, y,
                                                      color=scatter.get_facecolor()[0],
                                                      s=scatter.get_sizes()[0] if len(scatter.get_sizes()) > 0 else 20)
            
            # 复制标题和标签
            self.pareto_canvas.axes.set_xlabel(ax.get_xlabel())
            self.pareto_canvas.axes.set_ylabel(ax.get_ylabel())
            self.pareto_canvas.axes.set_title(ax.get_title())
            
            # 复制图例
            if ax.get_legend():
                self.pareto_canvas.axes.legend()
            
            # 仅处理第一个子图
            break
        
        # 重绘
        self.pareto_canvas.fig.tight_layout()
        self.pareto_canvas.draw()
        
        # 切换到帕累托前沿选项卡
        self.tab_widget.setCurrentWidget(self.pareto_tab)
    
    def export_current_figure(self):
        """导出当前图表"""
        # 获取当前选项卡
        current_tab = self.tab_widget.currentWidget()
        
        # 确定当前画布
        if current_tab == self.optimization_tab:
            canvas = self.optimization_canvas
            default_name = "optimization_process.png"
        elif current_tab == self.parameter_space_tab:
            canvas = self.parameter_space_canvas
            default_name = "parameter_space.png"
        elif current_tab == self.pareto_tab:
            canvas = self.pareto_canvas
            default_name = "pareto_front.png"
        else:
            return
        
        # 打开保存文件对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出图表", default_name,
            "PNG图像 (*.png);;JPEG图像 (*.jpg);;PDF文件 (*.pdf);;SVG图像 (*.svg);;所有文件 (*.*)"
        )
        
        if file_path:
            # 保存图形
            canvas.fig.savefig(file_path, dpi=300, bbox_inches='tight')
    
    def update_structures(self, structures):
        """更新结构列表
        
        Args:
            structures (dict): 分子结构字典，键为数据集名称，值为结构列表
        """
        self.structures = structures
        
        # 清除当前图形
        self.parameter_space_canvas.axes.clear()
        
        # 如果有结构，绘制第一个数据集的第一个结构的示意图
        if structures:
            # 获取第一个数据集的结构
            first_dataset = next(iter(structures.values()))
            if first_dataset:  # 确保数据集不为空
                structure = first_dataset[0] if isinstance(first_dataset, list) else first_dataset
                
                # 检查结构是否包含必要的数据
                if 'coordinates' in structure and 'atom_types' in structure:
                    coords = structure['coordinates']
                    atom_types = structure['atom_types']
                    
                    # 创建3D散点图
                    self.parameter_space_canvas.axes = self.parameter_space_canvas.fig.add_subplot(111, projection='3d')
                    scatter = self.parameter_space_canvas.axes.scatter(
                        coords[:, 0], coords[:, 1], coords[:, 2],
                        c=atom_types, cmap='viridis'
                    )
                    
                    # 添加颜色条
                    self.parameter_space_canvas.fig.colorbar(scatter, label='原子类型')
                    
                    # 设置轴标签
                    self.parameter_space_canvas.axes.set_xlabel('X (Å)')
                    self.parameter_space_canvas.axes.set_ylabel('Y (Å)')
                    self.parameter_space_canvas.axes.set_zlabel('Z (Å)')
                    
                    # 设置标题
                    dataset_name = next(iter(structures.keys()))
                    self.parameter_space_canvas.axes.set_title(f'分子结构示意图 - {dataset_name}')
                    
                    # 设置相等的坐标轴比例
                    self.parameter_space_canvas.axes.set_box_aspect([1,1,1])
        
        # 重绘
        self.parameter_space_canvas.fig.tight_layout()
        self.parameter_space_canvas.draw()
        
        # 切换到参数空间选项卡
        self.tab_widget.setCurrentWidget(self.parameter_space_tab)
    
    def update_parameter_space(self, search_path, current_best=None):
        """更新参数空间可视化
        
        Args:
            search_path (list): 搜索路径点列表，每个点是一个参数值字典
            current_best (dict, optional): 当前最优点
        """
        # 清除当前图形
        self.parameter_space_canvas.axes.clear()
        
        # 提取参数名称
        param_names = list(search_path[0].keys())
        
        if len(param_names) == 2:
            # 2D参数空间
            x = [point[param_names[0]] for point in search_path]
            y = [point[param_names[1]] for point in search_path]
            
            # 绘制搜索路径
            self.parameter_space_canvas.axes.plot(x, y, 'b-', alpha=0.5, label='搜索路径')
            self.parameter_space_canvas.axes.scatter(x, y, c='blue', s=20, alpha=0.5)
            
            # 标记当前最优点
            if current_best:
                self.parameter_space_canvas.axes.scatter(
                    current_best[param_names[0]],
                    current_best[param_names[1]],
                    c='red', s=100, marker='*', label='当前最优'
                )
            
            # 设置标签
            self.parameter_space_canvas.axes.set_xlabel(param_names[0])
            self.parameter_space_canvas.axes.set_ylabel(param_names[1])
            
        elif len(param_names) == 3:
            # 3D参数空间
            self.parameter_space_canvas.axes = self.parameter_space_canvas.fig.add_subplot(111, projection='3d')
            
            x = [point[param_names[0]] for point in search_path]
            y = [point[param_names[1]] for point in search_path]
            z = [point[param_names[2]] for point in search_path]
            
            # 绘制搜索路径
            self.parameter_space_canvas.axes.plot3D(x, y, z, 'b-', alpha=0.5, label='搜索路径')
            self.parameter_space_canvas.axes.scatter3D(x, y, z, c='blue', s=20, alpha=0.5)
            
            # 标记当前最优点
            if current_best:
                self.parameter_space_canvas.axes.scatter3D(
                    current_best[param_names[0]],
                    current_best[param_names[1]],
                    current_best[param_names[2]],
                    c='red', s=100, marker='*', label='当前最优'
                )
            
            # 设置标签
            self.parameter_space_canvas.axes.set_xlabel(param_names[0])
            self.parameter_space_canvas.axes.set_ylabel(param_names[1])
            self.parameter_space_canvas.axes.set_zlabel(param_names[2])
            
        else:
            # 超过3个参数时，选择前两个主要参数
            self.parameter_space_canvas.axes.text(
                0.5, 0.5, "参数空间维度过高\n显示前两个主要参数",
                ha='center', va='center'
            )
        
        # 添加图例
        self.parameter_space_canvas.axes.legend()
        
        # 设置标题
        self.parameter_space_canvas.axes.set_title("参数空间搜索路径")
        
        # 重绘
        self.parameter_space_canvas.fig.tight_layout()
        self.parameter_space_canvas.draw()
        
        # 切换到参数空间选项卡
        self.tab_widget.setCurrentWidget(self.parameter_space_tab)
    
    def update_pareto_front(self, pareto_points):
        """更新帕累托前沿可视化
        
        Args:
            pareto_points (list): 帕累托最优解列表，每个点是一个(obj1, obj2)元组
        """
        # 清除当前图形
        self.pareto_canvas.axes.clear()
        
        # 提取目标函数值
        obj1_values = [point[0] for point in pareto_points]
        obj2_values = [point[1] for point in pareto_points]
        
        # 绘制散点图
        self.pareto_canvas.axes.scatter(
            obj1_values, obj2_values,
            c='red', s=50, label='帕累托最优解'
        )
        
        # 按第一个目标函数值排序并连线
        sorted_indices = np.argsort(obj1_values)
        sorted_obj1 = [obj1_values[i] for i in sorted_indices]
        sorted_obj2 = [obj2_values[i] for i in sorted_indices]
        self.pareto_canvas.axes.plot(
            sorted_obj1, sorted_obj2,
            'r--', alpha=0.5, label='帕累托前沿'
        )
        
        # 设置标签
        self.pareto_canvas.axes.set_xlabel('目标1: 能量RMSE (kcal/mol)')
        self.pareto_canvas.axes.set_ylabel('目标2: 力RMSE (kcal/mol/Å)')
        self.pareto_canvas.axes.set_title('多目标优化帕累托前沿')
        
        # 添加图例
        self.pareto_canvas.axes.legend()
        
        # 重绘
        self.pareto_canvas.fig.tight_layout()
        self.pareto_canvas.draw()
        
        # 切换到帕累托前沿选项卡
        self.tab_widget.setCurrentWidget(self.pareto_tab)
    
    def save_all_figures(self, output_dir):
        """保存所有图表
        
        Args:
            output_dir (str): 输出目录
        """
        # 保存优化进程图
        self.optimization_canvas.fig.savefig(
            os.path.join(output_dir, "optimization_process.png"),
            dpi=300, bbox_inches='tight'
        )
        
        # 保存参数空间图
        self.parameter_space_canvas.fig.savefig(
            os.path.join(output_dir, "parameter_space.png"),
            dpi=300, bbox_inches='tight'
        )
        
        # 保存帕累托前沿图
        self.pareto_canvas.fig.savefig(
            os.path.join(output_dir, "pareto_front.png"),
            dpi=300, bbox_inches='tight'
        ) 