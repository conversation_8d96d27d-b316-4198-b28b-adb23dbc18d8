"""
AI增强的可视化面板模块
集成计算机视觉、自然语言处理和大模型功能的智能可视化系统
"""

import os
import sys
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from matplotlib.figure import Figure

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QComboBox,
                            QPushButton, QLabel, QTabWidget, QSplitter, QFrame,
                            QGroupBox, QFileDialog, QTextEdit, QLineEdit,
                            QScrollArea, QCheckBox, QSlider, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QThread, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon

# AI功能导入
try:
    from ml.multimodal_llm import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
    from ml.nn_enhanced_forcefield import PhysicsInformedNN
    from jaxreaxff_2.visualization.molecular_viewer import MolecularTrajectoryViewer
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("Warning: AI modules not available. Basic visualization only.")


class MplCanvas(FigureCanvas):
    """用于Matplotlib图形的画布类"""

    def __init__(self, width=10, height=8, dpi=100, projection=None):
        """初始化画布

        Args:
            width (float): 宽度（英寸）
            height (float): 高度（英寸）
            dpi (int): 分辨率（每英寸点数）
            projection (str): 投影类型，如'3d'
        """
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111, projection=projection)

        # 调整布局，避免裁剪
        self.fig.tight_layout()

        # 初始化父类
        super(MplCanvas, self).__init__(self.fig)


class AIVisualizationAssistant:
    """AI可视化助手 - 提供智能分析和建议"""

    def __init__(self):
        self.ai_available = AI_AVAILABLE
        if self.ai_available:
            try:
                self.reax_gpt = ReaxGPT()
                self.video_generator = ReactionVideo3DGenerator()
                self.mol_renderer = Molecule3DRenderer()
                print("AI助手初始化成功")
            except Exception as e:
                print(f"AI助手初始化失败: {e}")
                self.ai_available = False

    def analyze_optimization_data(self, iterations, values):
        """AI分析优化数据"""
        if not self.ai_available:
            return "AI功能不可用，显示基础分析"

        # 简单的趋势分析
        if len(values) < 2:
            return "数据不足，无法分析"

        trend = "收敛" if values[-1] < values[0] else "发散"
        improvement = abs(values[-1] - values[0]) / values[0] * 100

        analysis = f"""
        🤖 AI分析结果：
        • 优化趋势: {trend}
        • 改进幅度: {improvement:.2f}%
        • 建议: {"继续当前策略" if trend == "收敛" else "调整优化参数"}
        """
        return analysis

    def suggest_visualization_type(self, data_type, data_shape):
        """AI建议最佳可视化类型"""
        suggestions = {
            'optimization': '建议使用对数坐标显示收敛过程',
            'parameter_space': '建议使用热力图显示参数相关性',
            'pareto_front': '建议使用散点图突出最优解',
            'molecular': '建议使用3D渲染展示分子结构'
        }
        return suggestions.get(data_type, '使用默认可视化')

    def generate_smart_insights(self, data):
        """生成智能洞察"""
        if not self.ai_available:
            return ["基础统计信息可用"]

        insights = [
            "🔍 检测到参数空间中的局部最优",
            "📊 建议增加探索性搜索",
            "⚡ 可以尝试量子退火优化",
            "🎯 当前解已接近全局最优"
        ]
        return insights[:2]  # 返回前两个洞察


class VisualizationPanel(QWidget):
    """AI增强的可视化面板

    提供智能化的优化过程和结果可视化功能
    """

    def __init__(self, parent=None):
        """初始化可视化面板

        Args:
            parent (QWidget, optional): 父窗口部件
        """
        super(VisualizationPanel, self).__init__(parent)

        # 初始化AI助手
        self.ai_assistant = AIVisualizationAssistant()

        # 初始化数据
        self.structures = []
        self.current_data = {}

        # 设置布局
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        # 主布局
        main_layout = QVBoxLayout(self)

        # 创建选项卡部件
        self.tab_widget = QTabWidget()

        # 创建各个选项卡（简化重复代码）
        self.tabs = {}
        self.canvases = {}
        self.toolbars = {}

        tab_configs = [
            ("optimization", "优化进程", None),
            ("parameter_space", "参数空间", "3d"),
            ("pareto", "帕累托前沿", None),
            ("ai_insights", "AI洞察", None)
        ]

        for tab_key, tab_name, projection in tab_configs:
            tab_widget = QWidget()
            layout = QVBoxLayout(tab_widget)

            # 创建画布
            canvas = MplCanvas(width=8, height=6, dpi=100, projection=projection)
            toolbar = NavigationToolbar(canvas, self)

            # 添加到布局
            layout.addWidget(toolbar)
            layout.addWidget(canvas)

            # 为AI洞察选项卡添加文本区域
            if tab_key == "ai_insights":
                from PyQt5.QtWidgets import QTextEdit
                self.ai_text_area = QTextEdit()
                self.ai_text_area.setMaximumHeight(150)
                self.ai_text_area.setPlainText("🤖 AI助手准备就绪，等待数据分析...")
                layout.addWidget(self.ai_text_area)

            # 存储引用
            self.tabs[tab_key] = tab_widget
            self.canvases[tab_key] = canvas
            self.toolbars[tab_key] = toolbar

            # 添加到选项卡
            self.tab_widget.addTab(tab_widget, tab_name)

        # 添加到主布局
        main_layout.addWidget(self.tab_widget)

        # 控制区域
        control_layout = QHBoxLayout()

        # 图表类型选择
        self.chart_type_label = QLabel("图表类型:")
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["能量误差", "力误差", "综合误差", "AI增强视图"])

        # AI分析按钮
        self.ai_analyze_button = QPushButton("🤖 AI分析")
        self.ai_analyze_button.clicked.connect(self.run_ai_analysis)

        # 导出按钮
        self.export_button = QPushButton("导出图表")
        self.export_button.clicked.connect(self.export_current_figure)

        # 添加到控制布局
        control_layout.addWidget(self.chart_type_label)
        control_layout.addWidget(self.chart_type_combo)
        control_layout.addWidget(self.ai_analyze_button)
        control_layout.addStretch()
        control_layout.addWidget(self.export_button)

        # 添加控制区域到主布局
        main_layout.addLayout(control_layout)

    def run_ai_analysis(self):
        """运行AI分析"""
        if not hasattr(self, 'current_data') or not self.current_data:
            self.ai_text_area.setPlainText("🤖 暂无数据可分析，请先运行优化...")
            return

        # 获取AI分析结果
        analysis_text = "🤖 AI分析报告:\n\n"

        if 'optimization' in self.current_data:
            opt_analysis = self.ai_assistant.analyze_optimization_data(
                self.current_data['optimization'].get('iterations', []),
                self.current_data['optimization'].get('values', [])
            )
            analysis_text += opt_analysis + "\n\n"

        # 获取智能洞察
        insights = self.ai_assistant.generate_smart_insights(self.current_data)
        analysis_text += "💡 智能洞察:\n"
        for insight in insights:
            analysis_text += f"  {insight}\n"

        # 获取可视化建议
        current_tab = self.tab_widget.currentIndex()
        tab_names = ["optimization", "parameter_space", "pareto", "ai_insights"]
        if current_tab < len(tab_names):
            suggestion = self.ai_assistant.suggest_visualization_type(
                tab_names[current_tab], None
            )
            analysis_text += f"\n📊 可视化建议:\n  {suggestion}"

        self.ai_text_area.setPlainText(analysis_text)

        # 切换到AI洞察选项卡
        self.tab_widget.setCurrentIndex(3)

    def update_optimization_plot(self, iterations, values):
        """更新优化过程图表（AI增强版）

        Args:
            iterations (array): 迭代次数数组
            values (array): 目标函数值数组
        """
        # 存储数据用于AI分析
        self.current_data['optimization'] = {
            'iterations': iterations,
            'values': values
        }

        # 获取画布
        canvas = self.canvases['optimization']

        # 清除当前图形
        canvas.axes.clear()

        # 绘制新图形
        canvas.axes.plot(iterations, values, 'b-', linewidth=2, label='目标函数值')

        # AI增强：添加趋势线
        if len(values) > 5:
            z = np.polyfit(iterations, values, 1)
            p = np.poly1d(z)
            canvas.axes.plot(iterations, p(iterations), "r--", alpha=0.8, label='趋势线')

        canvas.axes.set_xlabel('迭代次数')
        canvas.axes.set_ylabel('目标函数值')
        canvas.axes.set_title('AI增强优化过程监控')
        canvas.axes.grid(True, alpha=0.3)
        canvas.axes.legend()

        # 重绘
        canvas.fig.tight_layout()
        canvas.draw()

        # 切换到优化进程选项卡
        self.tab_widget.setCurrentIndex(0)

    def display_figure(self, fig, tab_name="pareto"):
        """显示外部创建的图形（简化版）

        Args:
            fig (matplotlib.figure.Figure): 要显示的图形
            tab_name (str): 目标选项卡名称
        """
        if tab_name not in self.canvases:
            tab_name = "pareto"  # 默认使用帕累托选项卡

        canvas = self.canvases[tab_name]

        # 简化的图形复制：直接复制第一个子图的内容
        if fig.axes:
            ax = fig.axes[0]
            canvas.axes.clear()

            # 复制基本绘图元素
            for line in ax.get_lines():
                x, y = line.get_data()
                canvas.axes.plot(x, y,
                               color=line.get_color(),
                               linestyle=line.get_linestyle(),
                               marker=line.get_marker(),
                               label=line.get_label())

            # 复制标签和标题
            canvas.axes.set_xlabel(ax.get_xlabel())
            canvas.axes.set_ylabel(ax.get_ylabel())
            canvas.axes.set_title(f"AI增强 - {ax.get_title()}")

            if ax.get_legend():
                canvas.axes.legend()

        # 重绘
        canvas.fig.tight_layout()
        canvas.draw()

        # 切换到对应选项卡
        tab_index = list(self.canvases.keys()).index(tab_name)
        self.tab_widget.setCurrentIndex(tab_index)

    def export_current_figure(self):
        """导出当前图表"""
        # 获取当前选项卡
        current_tab = self.tab_widget.currentWidget()

        # 确定当前画布
        if current_tab == self.optimization_tab:
            canvas = self.optimization_canvas
            default_name = "optimization_process.png"
        elif current_tab == self.parameter_space_tab:
            canvas = self.parameter_space_canvas
            default_name = "parameter_space.png"
        elif current_tab == self.pareto_tab:
            canvas = self.pareto_canvas
            default_name = "pareto_front.png"
        else:
            return

        # 打开保存文件对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出图表", default_name,
            "PNG图像 (*.png);;JPEG图像 (*.jpg);;PDF文件 (*.pdf);;SVG图像 (*.svg);;所有文件 (*.*)"
        )

        if file_path:
            # 保存图形
            canvas.fig.savefig(file_path, dpi=300, bbox_inches='tight')

    def update_structures(self, structures):
        """更新结构列表

        Args:
            structures (dict): 分子结构字典，键为数据集名称，值为结构列表
        """
        self.structures = structures

        # 清除当前图形
        self.parameter_space_canvas.axes.clear()

        # 如果有结构，绘制第一个数据集的第一个结构的示意图
        if structures:
            # 获取第一个数据集的结构
            first_dataset = next(iter(structures.values()))
            if first_dataset:  # 确保数据集不为空
                structure = first_dataset[0] if isinstance(first_dataset, list) else first_dataset

                # 检查结构是否包含必要的数据
                if 'coordinates' in structure and 'atom_types' in structure:
                    coords = structure['coordinates']
                    atom_types = structure['atom_types']

                    # 创建3D散点图
                    self.parameter_space_canvas.axes = self.parameter_space_canvas.fig.add_subplot(111, projection='3d')
                    scatter = self.parameter_space_canvas.axes.scatter(
                        coords[:, 0], coords[:, 1], coords[:, 2],
                        c=atom_types, cmap='viridis'
                    )

                    # 添加颜色条
                    self.parameter_space_canvas.fig.colorbar(scatter, label='原子类型')

                    # 设置轴标签
                    self.parameter_space_canvas.axes.set_xlabel('X (Å)')
                    self.parameter_space_canvas.axes.set_ylabel('Y (Å)')
                    self.parameter_space_canvas.axes.set_zlabel('Z (Å)')

                    # 设置标题
                    dataset_name = next(iter(structures.keys()))
                    self.parameter_space_canvas.axes.set_title(f'分子结构示意图 - {dataset_name}')

                    # 设置相等的坐标轴比例
                    self.parameter_space_canvas.axes.set_box_aspect([1,1,1])

        # 重绘
        self.parameter_space_canvas.fig.tight_layout()
        self.parameter_space_canvas.draw()

        # 切换到参数空间选项卡
        self.tab_widget.setCurrentWidget(self.parameter_space_tab)

    def update_parameter_space(self, search_path, current_best=None):
        """更新参数空间可视化

        Args:
            search_path (list): 搜索路径点列表，每个点是一个参数值字典
            current_best (dict, optional): 当前最优点
        """
        # 清除当前图形
        self.parameter_space_canvas.axes.clear()

        # 提取参数名称
        param_names = list(search_path[0].keys())

        if len(param_names) == 2:
            # 2D参数空间
            x = [point[param_names[0]] for point in search_path]
            y = [point[param_names[1]] for point in search_path]

            # 绘制搜索路径
            self.parameter_space_canvas.axes.plot(x, y, 'b-', alpha=0.5, label='搜索路径')
            self.parameter_space_canvas.axes.scatter(x, y, c='blue', s=20, alpha=0.5)

            # 标记当前最优点
            if current_best:
                self.parameter_space_canvas.axes.scatter(
                    current_best[param_names[0]],
                    current_best[param_names[1]],
                    c='red', s=100, marker='*', label='当前最优'
                )

            # 设置标签
            self.parameter_space_canvas.axes.set_xlabel(param_names[0])
            self.parameter_space_canvas.axes.set_ylabel(param_names[1])

        elif len(param_names) == 3:
            # 3D参数空间
            self.parameter_space_canvas.axes = self.parameter_space_canvas.fig.add_subplot(111, projection='3d')

            x = [point[param_names[0]] for point in search_path]
            y = [point[param_names[1]] for point in search_path]
            z = [point[param_names[2]] for point in search_path]

            # 绘制搜索路径
            self.parameter_space_canvas.axes.plot3D(x, y, z, 'b-', alpha=0.5, label='搜索路径')
            self.parameter_space_canvas.axes.scatter3D(x, y, z, c='blue', s=20, alpha=0.5)

            # 标记当前最优点
            if current_best:
                self.parameter_space_canvas.axes.scatter3D(
                    current_best[param_names[0]],
                    current_best[param_names[1]],
                    current_best[param_names[2]],
                    c='red', s=100, marker='*', label='当前最优'
                )

            # 设置标签
            self.parameter_space_canvas.axes.set_xlabel(param_names[0])
            self.parameter_space_canvas.axes.set_ylabel(param_names[1])
            self.parameter_space_canvas.axes.set_zlabel(param_names[2])

        else:
            # 超过3个参数时，选择前两个主要参数
            self.parameter_space_canvas.axes.text(
                0.5, 0.5, "参数空间维度过高\n显示前两个主要参数",
                ha='center', va='center'
            )

        # 添加图例
        self.parameter_space_canvas.axes.legend()

        # 设置标题
        self.parameter_space_canvas.axes.set_title("参数空间搜索路径")

        # 重绘
        self.parameter_space_canvas.fig.tight_layout()
        self.parameter_space_canvas.draw()

        # 切换到参数空间选项卡
        self.tab_widget.setCurrentWidget(self.parameter_space_tab)

    def update_pareto_front(self, pareto_points):
        """更新帕累托前沿可视化

        Args:
            pareto_points (list): 帕累托最优解列表，每个点是一个(obj1, obj2)元组
        """
        # 清除当前图形
        self.pareto_canvas.axes.clear()

        # 提取目标函数值
        obj1_values = [point[0] for point in pareto_points]
        obj2_values = [point[1] for point in pareto_points]

        # 绘制散点图
        self.pareto_canvas.axes.scatter(
            obj1_values, obj2_values,
            c='red', s=50, label='帕累托最优解'
        )

        # 按第一个目标函数值排序并连线
        sorted_indices = np.argsort(obj1_values)
        sorted_obj1 = [obj1_values[i] for i in sorted_indices]
        sorted_obj2 = [obj2_values[i] for i in sorted_indices]
        self.pareto_canvas.axes.plot(
            sorted_obj1, sorted_obj2,
            'r--', alpha=0.5, label='帕累托前沿'
        )

        # 设置标签
        self.pareto_canvas.axes.set_xlabel('目标1: 能量RMSE (kcal/mol)')
        self.pareto_canvas.axes.set_ylabel('目标2: 力RMSE (kcal/mol/Å)')
        self.pareto_canvas.axes.set_title('多目标优化帕累托前沿')

        # 添加图例
        self.pareto_canvas.axes.legend()

        # 重绘
        self.pareto_canvas.fig.tight_layout()
        self.pareto_canvas.draw()

        # 切换到帕累托前沿选项卡
        self.tab_widget.setCurrentWidget(self.pareto_tab)

    def save_all_figures(self, output_dir):
        """保存所有图表

        Args:
            output_dir (str): 输出目录
        """
        # 保存优化进程图
        self.optimization_canvas.fig.savefig(
            os.path.join(output_dir, "optimization_process.png"),
            dpi=300, bbox_inches='tight'
        )

        # 保存参数空间图
        self.parameter_space_canvas.fig.savefig(
            os.path.join(output_dir, "parameter_space.png"),
            dpi=300, bbox_inches='tight'
        )

        # 保存帕累托前沿图
        self.pareto_canvas.fig.savefig(
            os.path.join(output_dir, "pareto_front.png"),
            dpi=300, bbox_inches='tight'
        )