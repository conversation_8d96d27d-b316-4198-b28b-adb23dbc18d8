#!/usr/bin/env python3
"""
快速修复脚本 - 让ReaxFFOpt能够立即运行
"""

import os

def fix_optimizer_init():
    """修复优化器初始化问题"""
    print("🔧 修复优化器初始化...")
    
    optimizer_init = "optimizer/__init__.py"
    
    # 创建简化版本
    content = '''"""
统一优化器模块 - 简化版
"""

# 导入基础优化器
try:
    from .optimizer import Optimizer, PSOOptimizer, create_optimizer
    from .multi_objective import MultiObjectiveOptimizer, MultiFidelityOptimizer
    from .adaptive_multiscale import AdaptiveMultiscaleOptimizer
    BASIC_OPTIMIZERS_AVAILABLE = True
except ImportError:
    BASIC_OPTIMIZERS_AVAILABLE = False
    print("Warning: 基础优化器不可用")

# 尝试导入AI优化器（可选）
AI_OPTIMIZERS_AVAILABLE = False
try:
    # 这些是可选的，不影响基本功能
    pass
except ImportError:
    pass

class UnifiedOptimizerFactory:
    """统一优化器工厂 - 简化版"""
    
    @staticmethod
    def create_optimizer(method: str, **kwargs):
        """创建优化器"""
        if not BASIC_OPTIMIZERS_AVAILABLE:
            raise RuntimeError("基础优化器不可用")
            
        if method == 'PSO':
            return PSOOptimizer(**kwargs)
        elif method == 'MultiObjective':
            return MultiObjectiveOptimizer(**kwargs)
        elif method == 'MultiFidelity':
            return MultiFidelityOptimizer(**kwargs)
        elif method == 'AdaptiveMultiscale':
            return AdaptiveMultiscaleOptimizer(**kwargs)
        else:
            available_methods = ['PSO', 'MultiObjective', 'MultiFidelity', 'AdaptiveMultiscale']
            raise ValueError(f"不支持的优化方法: {method}. 可用方法: {available_methods}")
    
    @staticmethod
    def get_available_methods():
        """获取可用的优化方法列表"""
        if BASIC_OPTIMIZERS_AVAILABLE:
            return ['PSO', 'MultiObjective', 'MultiFidelity', 'AdaptiveMultiscale']
        else:
            return []

# 向后兼容的函数
def get_optimizer_factory():
    """获取统一优化器工厂"""
    return UnifiedOptimizerFactory
'''
    
    with open(optimizer_init, "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ 已修复优化器初始化")

def fix_visualization_panel():
    """修复可视化面板的AI导入问题"""
    print("🔧 修复可视化面板AI导入...")
    
    viz_file = "gui/visualization_panel.py"
    
    # 读取文件
    with open(viz_file, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 替换AI导入部分
    old_ai_import = '''# AI功能导入 (简化版)
try:
    from ml.multimodal_llm import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
    from ml.nn_enhanced_forcefield import PhysicsInformedNN
    from jaxreaxff_2.visualization.molecular_viewer import MolecularTrajectoryViewer
    AI_AVAILABLE = True
except ImportError:
    try:
        from ml.multimodal_llm_simple import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
        AI_AVAILABLE = True
        print("使用简化版AI模块")
    except ImportError:
        AI_AVAILABLE = False
        print("Warning: AI modules not available. Basic visualization only.")'''
    
    new_ai_import = '''# AI功能导入 (安全版)
AI_AVAILABLE = False
try:
    from ml.multimodal_llm_simple import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
    AI_AVAILABLE = True
    print("✅ 使用简化版AI模块")
except ImportError:
    print("ℹ️  AI模块不可用，使用基础可视化功能")
    
    # 创建占位符类
    class ReaxGPT:
        def __init__(self): pass
        def analyze_optimization_data(self, *args): return "基础分析模式"
        def suggest_visualization_type(self, *args): return "使用默认可视化"
        def generate_smart_insights(self, *args): return ["基础统计信息可用"]
    
    class ReactionVideo3DGenerator:
        def __init__(self): pass
    
    class Molecule3DRenderer:
        def __init__(self): pass'''
    
    if old_ai_import in content:
        content = content.replace(old_ai_import, new_ai_import)
    else:
        # 如果没找到，就在文件开头添加
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'AI_AVAILABLE = False' in line:
                lines[i] = new_ai_import
                break
        content = '\n'.join(lines)
    
    # 写回文件
    with open(viz_file, "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ 已修复可视化面板AI导入")

def create_missing_modules():
    """创建缺失的模块"""
    print("🔧 创建缺失的模块...")
    
    # 确保automation目录存在
    if not os.path.exists("automation"):
        os.makedirs("automation")
        with open("automation/__init__.py", "w") as f:
            f.write('"""自动化模块"""')
    
    # 确保quantum目录存在
    if not os.path.exists("quantum"):
        os.makedirs("quantum")
        with open("quantum/__init__.py", "w") as f:
            f.write('"""量子计算模块"""')
    
    print("✅ 已创建缺失的模块目录")

def main():
    """主函数"""
    print("⚡ ReaxFFOpt 快速修复")
    print("=" * 30)
    
    # 1. 修复优化器
    fix_optimizer_init()
    
    # 2. 修复可视化面板
    fix_visualization_panel()
    
    # 3. 创建缺失模块
    create_missing_modules()
    
    print("\n🎉 快速修复完成！")
    print("\n🚀 现在可以运行: python main.py")
    print("📁 记得在GUI中选择 Datasets/cobalt 文件夹")

if __name__ == "__main__":
    main()
