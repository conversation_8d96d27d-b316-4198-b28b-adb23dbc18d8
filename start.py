#!/usr/bin/env python3
"""
ReaxFFOpt 启动脚本
"""

import sys
import os

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7+")
        return False
    
    # 检查关键包
    required_packages = ['numpy', 'matplotlib', 'PyQt5']
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️  缺少包: {missing}")
        print("请运行: pip install " + " ".join(missing))
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动 ReaxFFOpt")
    
    if not check_environment():
        print("❌ 环境检查失败")
        return 1
    
    # 启动主程序
    try:
        from main import main as main_func
        main_func()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
