"""
ReaxFFOpt 参数面板
管理力场参数的显示和编辑
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QTableWidget, QTableWidgetItem, QComboBox, QCheckBox,
                            QGroupBox, QSpinBox, QDoubleSpinBox, QHeaderView, 
                            QSplitter, QTabWidget, QFormLayout, QLineEdit, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QColor


class ParameterTable(QTableWidget):
    """参数表格组件，用于显示和编辑力场参数"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置表格属性
        self.setColumnCount(5)
        self.setHorizontalHeaderLabels(["参数名", "当前值", "范围", "优化", "敏感性"])
        self.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.verticalHeader().setVisible(False)
        self.setAlternatingRowColors(True)
        
        # 添加示例数据
        self.add_example_data()
    
    def add_example_data(self):
        """添加示例参数数据"""
        test_data = [
            ("p_val1", 1.2345, "0.8 - 1.5", True, 0.0),
            ("p_val2", 0.7654, "0.5 - 1.0", True, 0.0),
            ("p_bond1", 85.32, "80.0 - 90.0", False, 0.0),
            ("p_angle1", 2.456, "2.0 - 3.0", True, 0.0),
            ("p_tors1", 10.75, "5.0 - 15.0", False, 0.0),
            ("p_hbond1", 0.123, "0.05 - 0.2", True, 0.0),
        ]
        
        self.setRowCount(len(test_data))
        
        for row, (name, value, range_val, optimize, sensitivity) in enumerate(test_data):
            # 参数名
            self.setItem(row, 0, QTableWidgetItem(name))
            
            # 当前值
            value_item = QTableWidgetItem(f"{value:.4f}")
            value_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.setItem(row, 1, value_item)
            
            # 范围
            range_item = QTableWidgetItem(range_val)
            range_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 2, range_item)
            
            # 优化复选框
            checkbox = QCheckBox()
            checkbox.setChecked(optimize)
            checkbox_widget = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.addWidget(checkbox)
            checkbox_layout.setAlignment(Qt.AlignCenter)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            self.setCellWidget(row, 3, checkbox_widget)
            
            # 敏感性（初始为0）
            sensitivity_item = QTableWidgetItem(f"{sensitivity:.2f}")
            sensitivity_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.setItem(row, 4, sensitivity_item)
    
    def update_sensitivities(self, sensitivities):
        """更新参数敏感性值
        
        Args:
            sensitivities: 参数名称到敏感性值的字典
        """
        for row in range(self.rowCount()):
            param_name = self.item(row, 0).text()
            if param_name in sensitivities:
                # 更新敏感性值
                sensitivity = sensitivities[param_name]
                sensitivity_item = QTableWidgetItem(f"{sensitivity:.2f}")
                sensitivity_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                # 根据敏感性值设置背景色
                if sensitivity > 0.7:
                    sensitivity_item.setBackground(QColor(255, 200, 200))  # 高敏感性：浅红色
                elif sensitivity > 0.3:
                    sensitivity_item.setBackground(QColor(255, 255, 200))  # 中敏感性：浅黄色
                else:
                    sensitivity_item.setBackground(QColor(200, 255, 200))  # 低敏感性：浅绿色
                
                self.setItem(row, 4, sensitivity_item)
    
    def get_parameters_for_optimization(self):
        """获取标记为需要优化的参数列表"""
        parameters = []
        
        for row in range(self.rowCount()):
            param_name = self.item(row, 0).text()
            value = float(self.item(row, 1).text())
            checkbox_widget = self.cellWidget(row, 3)
            checkbox = checkbox_widget.findChild(QCheckBox)
            
            if checkbox.isChecked():
                range_text = self.item(row, 2).text()
                min_val, max_val = [float(x.strip()) for x in range_text.split('-')]
                
                parameters.append({
                    'name': param_name,
                    'value': value,
                    'min': min_val,
                    'max': max_val
                })
        
        return parameters


class OptimizationConfigPanel(QWidget):
    """优化配置面板，用于设置优化选项"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 优化方法设置
        method_group = QGroupBox("优化方法")
        method_layout = QFormLayout(method_group)
        
        self.method_combo = QComboBox()
        self.method_combo.addItems([
            "粒子群优化 (PSO)", 
            "差分进化 (DE)", 
            "遗传算法 (GA)", 
            "拟牛顿法 (BFGS)",
            "量子退火 (QA)",
            "多模态科学模型辅助优化"
        ])
        method_layout.addRow("优化算法:", self.method_combo)
        
        # 添加优化参数
        self.pop_size_spin = QSpinBox()
        self.pop_size_spin.setRange(10, 1000)
        self.pop_size_spin.setValue(50)
        self.pop_size_spin.setSingleStep(5)
        method_layout.addRow("种群大小:", self.pop_size_spin)
        
        self.max_iter_spin = QSpinBox()
        self.max_iter_spin.setRange(10, 10000)
        self.max_iter_spin.setValue(200)
        self.max_iter_spin.setSingleStep(10)
        method_layout.addRow("最大迭代次数:", self.max_iter_spin)
        
        self.tolerance_spin = QDoubleSpinBox()
        self.tolerance_spin.setRange(1e-10, 1e-1)
        self.tolerance_spin.setValue(1e-5)
        self.tolerance_spin.setDecimals(10)
        self.tolerance_spin.setSingleStep(1e-5)
        method_layout.addRow("收敛容差:", self.tolerance_spin)
        
        layout.addWidget(method_group)
        
        # 训练集配置
        training_group = QGroupBox("训练集配置")
        training_layout = QFormLayout(training_group)
        
        self.training_file_edit = QLineEdit()
        self.training_file_edit.setReadOnly(True)
        self.training_file_edit.setPlaceholderText("请通过'文件' > '导入' > '导入训练集'导入训练集文件")
        training_layout.addRow("训练集文件:", self.training_file_edit)
        
        self.weight_scheme_combo = QComboBox()
        self.weight_scheme_combo.addItems([
            "均等权重",
            "基于DFT能量加权",
            "结构重要性加权",
            "反应路径加权"
        ])
        training_layout.addRow("权重方案:", self.weight_scheme_combo)
        
        layout.addWidget(training_group)
        
        # 高级选项
        advanced_group = QGroupBox("高级选项")
        advanced_layout = QFormLayout(advanced_group)
        
        self.use_quantum_check = QCheckBox("使用量子加速")
        advanced_layout.addRow("", self.use_quantum_check)
        
        self.use_ai_check = QCheckBox("使用多模态科学模型辅助")
        advanced_layout.addRow("", self.use_ai_check)
        
        self.parallel_spin = QSpinBox()
        self.parallel_spin.setRange(1, 32)
        self.parallel_spin.setValue(4)
        advanced_layout.addRow("并行线程数:", self.parallel_spin)
        
        layout.addWidget(advanced_group)
        
        # 添加空白区域
        layout.addStretch()


class ParameterPanel(QWidget):
    """参数面板，包含参数表和控制按钮"""
    
    # 信号定义
    optimization_started = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建选项卡小部件
        tab_widget = QTabWidget()
        
        # 参数表选项卡
        param_tab = QWidget()
        param_layout = QVBoxLayout(param_tab)
        
        # 添加参数表
        self.parameter_table = ParameterTable()
        param_layout.addWidget(self.parameter_table)
        
        # 添加按钮区域
        buttons_layout = QHBoxLayout()
        
        self.select_all_button = QPushButton("全选")
        self.select_all_button.clicked.connect(self.select_all_parameters)
        buttons_layout.addWidget(self.select_all_button)
        
        self.deselect_all_button = QPushButton("全不选")
        self.deselect_all_button.clicked.connect(self.deselect_all_parameters)
        buttons_layout.addWidget(self.deselect_all_button)
        
        self.select_sensitive_button = QPushButton("选择敏感参数")
        self.select_sensitive_button.clicked.connect(self.select_sensitive_parameters)
        buttons_layout.addWidget(self.select_sensitive_button)
        
        param_layout.addLayout(buttons_layout)
        
        # 优化配置选项卡
        config_tab = QWidget()
        config_layout = QVBoxLayout(config_tab)
        self.config_panel = OptimizationConfigPanel()
        config_layout.addWidget(self.config_panel)
        
        # 添加选项卡
        tab_widget.addTab(param_tab, "参数")
        tab_widget.addTab(config_tab, "优化配置")
        
        main_layout.addWidget(tab_widget)
        
        # 添加控制按钮
        control_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始优化")
        self.start_button.setObjectName("startButton")
        self.start_button.clicked.connect(self.start_optimization)
        control_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止优化")
        self.stop_button.setObjectName("stopButton")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_optimization)
        control_layout.addWidget(self.stop_button)
        
        main_layout.addLayout(control_layout)
    
    def select_all_parameters(self):
        """选择所有参数进行优化"""
        for row in range(self.parameter_table.rowCount()):
            checkbox_widget = self.parameter_table.cellWidget(row, 3)
            checkbox = checkbox_widget.findChild(QCheckBox)
            checkbox.setChecked(True)
    
    def deselect_all_parameters(self):
        """取消选择所有参数"""
        for row in range(self.parameter_table.rowCount()):
            checkbox_widget = self.parameter_table.cellWidget(row, 3)
            checkbox = checkbox_widget.findChild(QCheckBox)
            checkbox.setChecked(False)
    
    def select_sensitive_parameters(self):
        """根据敏感性选择参数"""
        # 选择敏感度大于0.3的参数
        for row in range(self.parameter_table.rowCount()):
            sensitivity = float(self.parameter_table.item(row, 4).text())
            checkbox_widget = self.parameter_table.cellWidget(row, 3)
            checkbox = checkbox_widget.findChild(QCheckBox)
            
            if sensitivity > 0.3:  # 选择中高敏感度参数
                checkbox.setChecked(True)
            else:
                checkbox.setChecked(False)
    
    def set_parameter_sensitivities(self, sensitivities):
        """设置参数敏感性值
        
        Args:
            sensitivities: 参数名称到敏感性值的字典
        """
        self.parameter_table.update_sensitivities(sensitivities)
    
    def start_optimization(self):
        """开始优化过程"""
        # 获取需要优化的参数
        parameters = self.parameter_table.get_parameters_for_optimization()
        
        if not parameters:
            QMessageBox.warning(
                self, "参数错误", "没有选择要优化的参数。请选择至少一个参数。"
            )
            return
        
        # 获取选中的优化方法
        config = {
            'method': self.config_panel.method_combo.currentText(),
            'population_size': self.config_panel.pop_size_spin.value(),
            'max_iterations': self.config_panel.max_iter_spin.value(),
            'tolerance': self.config_panel.tolerance_spin.value(),
            'use_quantum': self.config_panel.use_quantum_check.isChecked(),
            'use_ai_model': self.config_panel.use_ai_check.isChecked(),
            'parallel_threads': self.config_panel.parallel_spin.value(),
            'parameters': parameters
        }
        
        # 发出信号，传递配置信息
        self.optimization_started.emit(config)
        
        # 更新按钮状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
    
    def stop_optimization(self):
        """停止优化过程"""
        # 更新按钮状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False) 