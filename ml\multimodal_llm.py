"""
大模型赋能的反应机理智能解析
多模态反应路径生成、知识图谱和自然语言处理
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import (
    AutoModel, AutoTokenizer,
    VisionEncoderDecoderModel, ViTImageProcessor,
    T5ForConditionalGeneration, T5Tokenizer
)
from diffusers import StableDiffusionPipeline, DDPMScheduler
import networkx as nx
from typing import Dict, List, Tuple, Optional, Union
import cv2
import logging
from dataclasses import dataclass
import rdkit
from rdkit import Chem
from rdkit.Chem import Draw
import py3Dmol

logger = logging.getLogger(__name__)


class ReactionPathTransformer(nn.Module):
    """Transformer模型提取反应路径时序特征"""

    def __init__(self, d_model: int = 512, n_heads: int = 8,
                 n_layers: int = 6, max_seq_len: int = 1000):
        super().__init__()

        self.d_model = d_model
        self.max_seq_len = max_seq_len

        # 位置编码
        self.pos_encoding = self._create_positional_encoding(max_seq_len, d_model)

        # 分子结构编码器
        self.structure_encoder = nn.Sequential(
            nn.Linear(3, d_model // 2),  # 3D坐标
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model)
        )

        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=2048,
            dropout=0.1
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, n_layers)

        # 反应事件分类头
        self.event_classifier = nn.Linear(d_model, 5)  # 5种反应类型

        # 中间体识别头
        self.intermediate_detector = nn.Sequential(
            nn.Linear(d_model, 256),
            nn.ReLU(),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )

    def _create_positional_encoding(self, max_len: int, d_model: int) -> torch.Tensor:
        """创建位置编码"""
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len).unsqueeze(1).float()

        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           -(np.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        return pe.unsqueeze(0)

    def forward(self, trajectory: torch.Tensor) -> Dict[str, torch.Tensor]:
        """前向传播

        Args:
            trajectory: 反应轨迹 [batch_size, seq_len, n_atoms, 3]

        Returns:
            特征字典
        """
        batch_size, seq_len, n_atoms, _ = trajectory.shape

        # 编码每个时间步的结构
        encoded_steps = []
        for t in range(seq_len):
            # 对每个原子编码
            atoms_t = trajectory[:, t]  # [batch_size, n_atoms, 3]
            atoms_encoded = self.structure_encoder(atoms_t)  # [batch_size, n_atoms, d_model]

            # 聚合原子特征
            step_feature = atoms_encoded.mean(dim=1)  # [batch_size, d_model]
            encoded_steps.append(step_feature)

        # 堆叠时间步
        x = torch.stack(encoded_steps, dim=1)  # [batch_size, seq_len, d_model]

        # 添加位置编码
        x = x + self.pos_encoding[:, :seq_len, :]

        # Transformer编码
        x = x.transpose(0, 1)  # [seq_len, batch_size, d_model]
        encoded = self.transformer_encoder(x)
        encoded = encoded.transpose(0, 1)  # [batch_size, seq_len, d_model]

        # 反应事件分类
        event_logits = self.event_classifier(encoded)

        # 中间体检测
        intermediate_probs = self.intermediate_detector(encoded)

        return {
            'features': encoded,
            'event_logits': event_logits,
            'intermediate_probs': intermediate_probs
        }

    def identify_reaction_events(self, trajectory: torch.Tensor) -> List[Dict]:
        """识别反应事件

        Returns:
            反应事件列表
        """
        output = self.forward(trajectory)

        # 获取事件类型
        event_types = torch.argmax(output['event_logits'], dim=-1)

        # 获取中间体
        intermediate_mask = output['intermediate_probs'] > 0.5

        events = []
        for t in range(event_types.shape[1]):
            if event_types[0, t] != 0:  # 非空事件
                event = {
                    'time': t,
                    'type': self._event_type_name(event_types[0, t].item()),
                    'is_intermediate': intermediate_mask[0, t, 0].item()
                }
                events.append(event)

        return events

    def _event_type_name(self, event_id: int) -> str:
        """事件类型名称映射"""
        event_names = {
            0: 'none',
            1: 'bond_formation',
            2: 'bond_breaking',
            3: 'charge_transfer',
            4: 'conformational_change'
        }
        return event_names.get(event_id, 'unknown')


class ReactionVideo3DGenerator:
    """基于扩散模型的3D反应视频生成"""

    def __init__(self, model_name: str = "stabilityai/stable-diffusion-2-1"):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 加载预训练的稳定扩散模型
        self.pipe = StableDiffusionPipeline.from_pretrained(
            model_name,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
        )
        self.pipe = self.pipe.to(self.device)

        # 自定义的3D渲染器
        self.renderer_3d = Molecule3DRenderer()

    def generate_reaction_video(self, reaction_path: List[Dict],
                              fps: int = 30, duration: float = 10.0) -> np.ndarray:
        """生成反应路径的3D视频

        Args:
            reaction_path: 反应路径（包含分子构型）
            fps: 帧率
            duration: 视频时长（秒）

        Returns:
            视频帧数组 [n_frames, height, width, 3]
        """
        n_frames = int(fps * duration)
        frames = []

        # 插值反应路径
        interpolated_path = self._interpolate_path(reaction_path, n_frames)

        for i, config in enumerate(interpolated_path):
            # 渲染3D分子
            mol_image = self.renderer_3d.render(config)

            # 使用扩散模型增强视觉效果
            prompt = self._generate_prompt(config, i / n_frames)
            enhanced_image = self._enhance_with_diffusion(mol_image, prompt)

            frames.append(enhanced_image)

        return np.array(frames)

    def _interpolate_path(self, path: List[Dict], n_frames: int) -> List[Dict]:
        """插值反应路径"""
        if len(path) >= n_frames:
            # 下采样
            indices = np.linspace(0, len(path) - 1, n_frames).astype(int)
            return [path[i] for i in indices]

        # 上采样（线性插值）
        interpolated = []
        for i in range(n_frames):
            t = i / (n_frames - 1) * (len(path) - 1)
            idx = int(t)

            if idx >= len(path) - 1:
                interpolated.append(path[-1])
            else:
                # 线性插值
                alpha = t - idx
                config = self._interpolate_configs(path[idx], path[idx + 1], alpha)
                interpolated.append(config)

        return interpolated

    def _interpolate_configs(self, config1: Dict, config2: Dict, alpha: float) -> Dict:
        """插值两个分子构型"""
        coords1 = np.array(config1['coordinates'])
        coords2 = np.array(config2['coordinates'])

        # 确保原子数相同
        assert coords1.shape == coords2.shape

        # 线性插值坐标
        interpolated_coords = (1 - alpha) * coords1 + alpha * coords2

        return {
            'atoms': config1['atoms'],
            'coordinates': interpolated_coords,
            'bonds': config1.get('bonds', [])
        }

    def _generate_prompt(self, config: Dict, progress: float) -> str:
        """生成扩散模型的提示词"""
        # 基于反应进度生成描述
        if progress < 0.3:
            phase = "initial reactants"
        elif progress < 0.7:
            phase = "transition state"
        else:
            phase = "final products"

        # 分析分子特征
        n_atoms = len(config['atoms'])

        prompt = (f"High quality 3D molecular visualization, {phase}, "
                 f"{n_atoms} atoms, chemical reaction, "
                 "scientific rendering, detailed bonds, "
                 "glowing energy field, dark background")

        return prompt

    def _enhance_with_diffusion(self, base_image: np.ndarray, prompt: str) -> np.ndarray:
        """使用扩散模型增强图像"""
        # 将numpy图像转换为PIL
        from PIL import Image
        pil_image = Image.fromarray(base_image)

        # 使用img2img增强
        enhanced = self.pipe(
            prompt=prompt,
            image=pil_image,
            strength=0.3,  # 保留原始结构
            guidance_scale=7.5,
            num_inference_steps=20
        ).images[0]

        return np.array(enhanced)


class Molecule3DRenderer:
    """3D分子渲染器"""

    def render(self, config: Dict, size: Tuple[int, int] = (512, 512)) -> np.ndarray:
        """渲染3D分子结构

        Args:
            config: 分子配置（原子、坐标、键）
            size: 输出图像尺寸

        Returns:
            渲染的图像
        """
        # 创建3D场景
        view = py3Dmol.view(width=size[0], height=size[1])

        # 添加原子
        for i, (atom, coord) in enumerate(zip(config['atoms'], config['coordinates'])):
            view.addSphere({
                'center': {'x': coord[0], 'y': coord[1], 'z': coord[2]},
                'radius': self._get_atom_radius(atom),
                'color': self._get_atom_color(atom)
            })

        # 添加键
        if 'bonds' in config:
            for bond in config['bonds']:
                i, j = bond['atoms']
                coord1 = config['coordinates'][i]
                coord2 = config['coordinates'][j]

                view.addCylinder({
                    'start': {'x': coord1[0], 'y': coord1[1], 'z': coord1[2]},
                    'end': {'x': coord2[0], 'y': coord2[1], 'z': coord2[2]},
                    'radius': 0.1,
                    'color': 'gray'
                })

        # 设置样式和渲染
        view.setStyle({'stick': {}})
        view.zoomTo()

        # 获取图像（这里简化处理，实际需要从3D视图捕获）
        # 在实际实现中，需要使用截图或其他方法
        image = np.ones((size[1], size[0], 3), dtype=np.uint8) * 255

        return image

    def _get_atom_radius(self, atom: str) -> float:
        """获取原子半径"""
        radii = {
            'H': 0.3, 'C': 0.7, 'N': 0.65,
            'O': 0.6, 'F': 0.5, 'S': 1.0
        }
        return radii.get(atom, 0.7)

    def _get_atom_color(self, atom: str) -> str:
        """获取原子颜色"""
        colors = {
            'H': 'white', 'C': 'gray', 'N': 'blue',
            'O': 'red', 'F': 'green', 'S': 'yellow'
        }
        return colors.get(atom, 'gray')


class ReactionKnowledgeGraph:
    """反应机理知识图谱"""

    def __init__(self):
        self.graph = nx.DiGraph()
        self.entity_embeddings = {}
        self.relation_embeddings = {}

        # 预训练的分子编码器
        self.mol_encoder = MolecularEncoder()

    def add_reaction(self, reactants: List[str], products: List[str],
                    conditions: Dict = None, mechanism: str = None):
        """添加反应到知识图谱

        Args:
            reactants: 反应物SMILES列表
            products: 产物SMILES列表
            conditions: 反应条件
            mechanism: 反应机理描述
        """
        # 创建反应节点
        reaction_id = f"rxn_{len(self.graph)}"
        self.graph.add_node(reaction_id, type='reaction',
                          mechanism=mechanism, conditions=conditions)

        # 添加反应物
        for smiles in reactants:
            mol_id = f"mol_{smiles}"
            if mol_id not in self.graph:
                self.graph.add_node(mol_id, type='molecule', smiles=smiles)
            self.graph.add_edge(mol_id, reaction_id, relation='reactant')

        # 添加产物
        for smiles in products:
            mol_id = f"mol_{smiles}"
            if mol_id not in self.graph:
                self.graph.add_node(mol_id, type='molecule', smiles=smiles)
            self.graph.add_edge(reaction_id, mol_id, relation='product')

    def embed_entities(self):
        """计算实体嵌入"""
        for node_id, data in self.graph.nodes(data=True):
            if data['type'] == 'molecule':
                # 使用分子编码器
                smiles = data['smiles']
                embedding = self.mol_encoder.encode(smiles)
                self.entity_embeddings[node_id] = embedding
            elif data['type'] == 'reaction':
                # 聚合反应物和产物嵌入
                reactant_embeds = []
                product_embeds = []

                for neighbor in self.graph.predecessors(node_id):
                    if neighbor in self.entity_embeddings:
                        reactant_embeds.append(self.entity_embeddings[neighbor])

                for neighbor in self.graph.successors(node_id):
                    if neighbor in self.entity_embeddings:
                        product_embeds.append(self.entity_embeddings[neighbor])

                if reactant_embeds and product_embeds:
                    reactant_avg = np.mean(reactant_embeds, axis=0)
                    product_avg = np.mean(product_embeds, axis=0)
                    self.entity_embeddings[node_id] = np.concatenate([reactant_avg, product_avg])

    def query_similar_reactions(self, query_smiles: List[str], top_k: int = 5) -> List[Dict]:
        """查询相似反应

        Args:
            query_smiles: 查询分子的SMILES
            top_k: 返回前k个相似反应

        Returns:
            相似反应列表
        """
        # 编码查询分子
        query_embeddings = [self.mol_encoder.encode(s) for s in query_smiles]
        query_embedding = np.mean(query_embeddings, axis=0)

        # 计算相似度
        similarities = []
        for node_id, embedding in self.entity_embeddings.items():
            if self.graph.nodes[node_id]['type'] == 'reaction':
                sim = np.dot(query_embedding, embedding[:len(query_embedding)]) / \
                      (np.linalg.norm(query_embedding) * np.linalg.norm(embedding[:len(query_embedding)]))
                similarities.append((node_id, sim))

        # 排序并返回
        similarities.sort(key=lambda x: x[1], reverse=True)

        results = []
        for rxn_id, sim in similarities[:top_k]:
            # 获取反应详情
            reactants = []
            products = []

            for neighbor in self.graph.predecessors(rxn_id):
                if self.graph.nodes[neighbor]['type'] == 'molecule':
                    reactants.append(self.graph.nodes[neighbor]['smiles'])

            for neighbor in self.graph.successors(rxn_id):
                if self.graph.nodes[neighbor]['type'] == 'molecule':
                    products.append(self.graph.nodes[neighbor]['smiles'])

            results.append({
                'reaction_id': rxn_id,
                'reactants': reactants,
                'products': products,
                'similarity': sim,
                'mechanism': self.graph.nodes[rxn_id].get('mechanism')
            })

        return results


class MolecularEncoder(nn.Module):
    """分子编码器"""

    def __init__(self, embedding_dim: int = 256):
        super().__init__()
        self.embedding_dim = embedding_dim

        # 简化的分子指纹编码器
        self.encoder = nn.Sequential(
            nn.Linear(2048, 512),  # Morgan指纹维度
            nn.ReLU(),
            nn.Linear(512, embedding_dim),
            nn.LayerNorm(embedding_dim)
        )

    def encode(self, smiles: str) -> np.ndarray:
        """编码SMILES字符串"""
        # 计算分子指纹
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return np.zeros(self.embedding_dim)

        # Morgan指纹
        from rdkit.Chem import AllChem
        fp = AllChem.GetMorganFingerprintAsBitVect(mol, radius=2, nBits=2048)
        fp_array = np.array(fp)

        # 通过网络编码
        with torch.no_grad():
            fp_tensor = torch.FloatTensor(fp_array).unsqueeze(0)
            embedding = self.encoder(fp_tensor).squeeze(0).numpy()

        return embedding


class ReaxGPT:
    """ReaxFF专用的多模态大语言模型"""

    def __init__(self, model_name: str = "google/flan-t5-large"):
        # 加载预训练语言模型
        self.tokenizer = T5Tokenizer.from_pretrained(model_name)
        self.model = T5ForConditionalGeneration.from_pretrained(model_name)

        # 反应路径编码器
        self.path_encoder = ReactionPathTransformer()

        # 知识图谱
        self.knowledge_graph = ReactionKnowledgeGraph()

        # 视频生成器
        self.video_generator = ReactionVideo3DGenerator()

    def text_to_reaxff_params(self, description: str) -> Dict:
        """从文本描述生成ReaxFF参数

        Args:
            description: 自然语言描述

        Returns:
            ReaxFF参数字典
        """
        # 构建提示
        prompt = f"""Convert the following chemical reaction description to ReaxFF parameters:

        Description: {description}

        Generate parameters for:
        1. Bond parameters (sigma, pi, double pi)
        2. Angle parameters
        3. Torsion parameters
        4. van der Waals parameters
        5. Charge parameters

        Output format: parameter_name: value
        """

        # 生成参数文本
        inputs = self.tokenizer(prompt, return_tensors="pt", max_length=512, truncation=True)
        outputs = self.model.generate(**inputs, max_length=512, temperature=0.7)
        param_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

        # 解析参数
        params = self._parse_param_text(param_text)

        return params

    def analyze_reaction_mechanism(self, trajectory: np.ndarray,
                                 return_video: bool = False) -> Dict:
        """分析反应机理

        Args:
            trajectory: 反应轨迹
            return_video: 是否生成视频

        Returns:
            机理分析结果
        """
        # 提取反应事件
        trajectory_tensor = torch.FloatTensor(trajectory).unsqueeze(0)
        events = self.path_encoder.identify_reaction_events(trajectory_tensor)

        # 生成机理描述
        mechanism_text = self._generate_mechanism_description(events)

        # 查询相似反应
        # （这里需要从轨迹提取分子结构，简化处理）
        similar_reactions = []

        result = {
            'events': events,
            'mechanism_description': mechanism_text,
            'similar_reactions': similar_reactions
        }

        # 生成视频
        if return_video:
            # 将轨迹转换为所需格式
            reaction_path = self._trajectory_to_path(trajectory)
            video = self.video_generator.generate_reaction_video(reaction_path)
            result['video'] = video

        return result

    def _parse_param_text(self, text: str) -> Dict:
        """解析参数文本"""
        params = {}

        lines = text.strip().split('\n')
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip()
                value = value.strip()

                try:
                    # 尝试转换为数值
                    params[key] = float(value)
                except ValueError:
                    params[key] = value

        return params

    def _generate_mechanism_description(self, events: List[Dict]) -> str:
        """生成机理描述"""
        descriptions = []

        for event in events:
            time = event['time']
            event_type = event['type']
            is_intermediate = event['is_intermediate']

            if event_type == 'bond_formation':
                desc = f"At step {time}, a new bond forms"
            elif event_type == 'bond_breaking':
                desc = f"At step {time}, a bond breaks"
            elif event_type == 'charge_transfer':
                desc = f"At step {time}, charge transfer occurs"
            else:
                desc = f"At step {time}, {event_type} occurs"

            if is_intermediate:
                desc += " (intermediate detected)"

            descriptions.append(desc)

        return ". ".join(descriptions)

    def _trajectory_to_path(self, trajectory: np.ndarray) -> List[Dict]:
        """将轨迹转换为路径格式"""
        path = []

        for t in range(trajectory.shape[0]):
            config = {
                'atoms': ['C'] * trajectory.shape[1],  # 简化：假设都是碳原子
                'coordinates': trajectory[t],
                'bonds': []  # 需要根据距离计算
            }
            path.append(config)

        return path

    def chat_interface(self, user_input: str, context: Dict = None) -> str:
        """自然语言聊天接口

        Args:
            user_input: 用户输入
            context: 上下文信息

        Returns:
            AI回复
        """
        # 构建提示
        if context:
            prompt = f"""
            你是ReaxFF力场参数优化的专家助手。

            当前上下文:
            - 优化状态: {context.get('optimization_status', '未知')}
            - 当前误差: {context.get('current_error', '未知')}
            - 迭代次数: {context.get('iterations', '未知')}

            用户问题: {user_input}

            请提供专业的建议和解答:
            """
        else:
            prompt = f"""
            你是ReaxFF力场参数优化的专家助手。

            用户问题: {user_input}

            请提供专业的建议和解答:
            """

        # 生成回复
        inputs = self.tokenizer(prompt, return_tensors="pt", max_length=512, truncation=True)
        outputs = self.model.generate(
            inputs.input_ids,
            max_length=256,
            num_beams=4,
            temperature=0.7,
            do_sample=True
        )

        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

        # 移除提示部分，只返回回复
        if "请提供专业的建议和解答:" in response:
            response = response.split("请提供专业的建议和解答:")[-1].strip()

        return response

    def advanced_query_interface(self, query: str, query_type: str = "general") -> Dict:
        """高级查询接口

        Args:
            query: 查询内容
            query_type: 查询类型 ('parameter', 'mechanism', 'optimization', 'general')

        Returns:
            结构化的查询结果
        """

        if query_type == "parameter":
            return self._handle_parameter_query(query)
        elif query_type == "mechanism":
            return self._handle_mechanism_query(query)
        elif query_type == "optimization":
            return self._handle_optimization_query(query)
        else:
            return self._handle_general_query(query)

    def _handle_parameter_query(self, query: str) -> Dict:
        """处理参数相关查询"""

        # 参数知识库
        parameter_knowledge = {
            'p_val1': {
                'description': '原子价参数，控制原子的价电子行为',
                'typical_range': '0.1 - 10.0',
                'sensitivity': 'high',
                'related_properties': ['键能', '键长', '电荷分布']
            },
            'p_bond1': {
                'description': '键参数，影响共价键的强度',
                'typical_range': '50.0 - 200.0',
                'sensitivity': 'medium',
                'related_properties': ['键解离能', '键振动频率']
            },
            'p_angle1': {
                'description': '角度参数，控制键角的偏好',
                'typical_range': '1.0 - 10.0',
                'sensitivity': 'medium',
                'related_properties': ['分子几何', '角度弯曲能']
            }
        }

        # 简单的关键词匹配
        found_params = []
        for param_name, param_info in parameter_knowledge.items():
            if param_name.lower() in query.lower():
                found_params.append({
                    'name': param_name,
                    'info': param_info
                })

        # 生成自然语言回复
        if found_params:
            response = "找到以下相关参数信息：\n"
            for param in found_params:
                response += f"\n• {param['name']}: {param['info']['description']}\n"
                response += f"  典型范围: {param['info']['typical_range']}\n"
                response += f"  敏感性: {param['info']['sensitivity']}\n"
        else:
            response = self.chat_interface(f"请解释ReaxFF参数: {query}")

        return {
            'query_type': 'parameter',
            'found_parameters': found_params,
            'response': response,
            'suggestions': self._get_parameter_suggestions(query)
        }

    def _handle_mechanism_query(self, query: str) -> Dict:
        """处理反应机理查询"""

        # 使用知识图谱查询相似反应
        similar_reactions = self.knowledge_graph.query_similar_reactions([query])

        # 生成机理解释
        mechanism_explanation = self._generate_mechanism_explanation_from_query(query)

        return {
            'query_type': 'mechanism',
            'similar_reactions': similar_reactions,
            'mechanism_explanation': mechanism_explanation,
            'visualization_suggestions': [
                '生成3D反应路径动画',
                '显示能量曲线图',
                '分析中间体结构'
            ]
        }

    def _handle_optimization_query(self, query: str) -> Dict:
        """处理优化相关查询"""

        optimization_advice = {
            'convergence_issues': [
                '检查参数边界设置',
                '调整优化算法参数',
                '增加训练数据多样性',
                '使用多目标优化策略'
            ],
            'parameter_selection': [
                '基于敏感性分析选择关键参数',
                '使用贝叶斯优化指导参数搜索',
                '考虑参数间的相关性'
            ],
            'performance_improvement': [
                '使用强化学习优化器',
                '启用量子退火算法',
                '应用神经网络增强力场'
            ]
        }

        # 根据查询内容匹配建议
        relevant_advice = []
        for category, advice_list in optimization_advice.items():
            if any(keyword in query.lower() for keyword in category.split('_')):
                relevant_advice.extend(advice_list)

        response = self.chat_interface(f"关于优化问题: {query}")

        return {
            'query_type': 'optimization',
            'response': response,
            'specific_advice': relevant_advice,
            'recommended_methods': self._recommend_optimization_methods(query)
        }

    def _handle_general_query(self, query: str) -> Dict:
        """处理一般查询"""

        response = self.chat_interface(query)

        # 分析查询意图
        intent = self._analyze_query_intent(query)

        return {
            'query_type': 'general',
            'response': response,
            'detected_intent': intent,
            'follow_up_suggestions': self._generate_follow_up_suggestions(intent)
        }

    def _get_parameter_suggestions(self, query: str) -> List[str]:
        """获取参数相关建议"""
        return [
            '使用敏感性分析确定关键参数',
            '参考文献中的典型参数值',
            '考虑参数的物理意义',
            '使用贝叶斯优化进行参数搜索'
        ]

    def _generate_mechanism_explanation_from_query(self, query: str) -> str:
        """生成反应机理解释"""
        return f"基于查询'{query}'，这是一个涉及多步骤的反应过程，包括初始反应物结合、过渡态形成、中间体稳定化和最终产物生成等阶段。"

    def _recommend_optimization_methods(self, query: str) -> List[str]:
        """推荐优化方法"""
        methods = ['PSO粒子群优化', '遗传算法', '强化学习', '量子退火']

        # 根据查询内容推荐
        if 'fast' in query.lower() or '快速' in query:
            return ['PSO粒子群优化', '梯度下降']
        elif 'global' in query.lower() or '全局' in query:
            return ['遗传算法', '量子退火']
        elif 'adaptive' in query.lower() or '自适应' in query:
            return ['强化学习', '贝叶斯优化']
        else:
            return methods

    def _analyze_query_intent(self, query: str) -> str:
        """分析查询意图"""
        if any(word in query.lower() for word in ['how', 'why', '如何', '为什么']):
            return 'explanation_seeking'
        elif any(word in query.lower() for word in ['best', 'optimal', '最好', '最优']):
            return 'recommendation_seeking'
        elif any(word in query.lower() for word in ['problem', 'error', '问题', '错误']):
            return 'troubleshooting'
        else:
            return 'information_seeking'

    def _generate_follow_up_suggestions(self, intent: str) -> List[str]:
        """生成后续建议"""
        suggestions_map = {
            'explanation_seeking': [
                '需要更详细的技术解释吗？',
                '想了解相关的理论背景吗？',
                '需要查看具体的计算示例吗？'
            ],
            'recommendation_seeking': [
                '需要具体的参数推荐吗？',
                '想了解不同方法的比较吗？',
                '需要性能基准测试结果吗？'
            ],
            'troubleshooting': [
                '需要详细的错误诊断吗？',
                '想查看常见问题解决方案吗？',
                '需要专家支持联系方式吗？'
            ],
            'information_seeking': [
                '需要更多相关资料吗？',
                '想了解最新研究进展吗？',
                '需要查看使用教程吗？'
            ]
        }

        return suggestions_map.get(intent, ['有其他问题需要帮助吗？'])