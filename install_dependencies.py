#!/usr/bin/env python3
"""
ReaxFFOpt 依赖安装脚本
自动安装所需的Python包
"""

import subprocess
import sys
import os
import importlib

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def install_package(package_name):
    """安装Python包"""
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package_name} 安装失败")
        return False

def main():
    """主函数"""
    print("🚀 ReaxFFOpt 依赖检查和安装")
    print("=" * 50)
    
    # 基础依赖包
    basic_packages = [
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("scipy", "scipy"),
        ("pandas", "pandas"),
        ("PyQt5", "PyQt5"),
        ("networkx", "networkx"),
        ("scikit-learn", "sklearn"),
    ]
    
    # AI/ML依赖包
    ai_packages = [
        ("torch", "torch"),
        ("jax", "jax"),
        ("jaxlib", "jaxlib"),
        ("haiku-dm", "haiku"),
        ("optax", "optax"),
        ("transformers", "transformers"),
        ("diffusers", "diffusers"),
        ("opencv-python", "cv2"),
        ("rdkit", "rdkit"),
        ("py3Dmol", "py3Dmol"),
        ("Pillow", "PIL"),
    ]
    
    # 可选依赖包
    optional_packages = [
        ("tensorboard", "tensorboard"),
        ("wandb", "wandb"),
        ("plotly", "plotly"),
        ("seaborn", "seaborn"),
    ]
    
    print("\n📦 检查基础依赖包...")
    missing_basic = []
    for package, import_name in basic_packages:
        if not check_package(package, import_name):
            missing_basic.append(package)
    
    print("\n🤖 检查AI/ML依赖包...")
    missing_ai = []
    for package, import_name in ai_packages:
        if not check_package(package, import_name):
            missing_ai.append(package)
    
    print("\n🔧 检查可选依赖包...")
    missing_optional = []
    for package, import_name in optional_packages:
        if not check_package(package, import_name):
            missing_optional.append(package)
    
    # 安装缺失的包
    all_missing = missing_basic + missing_ai
    
    if not all_missing:
        print("\n🎉 所有必需的依赖包都已安装！")
        return
    
    print(f"\n📥 需要安装 {len(all_missing)} 个包...")
    
    # 首先安装基础包
    if missing_basic:
        print("\n安装基础依赖包...")
        for package in missing_basic:
            install_package(package)
    
    # 然后安装AI包
    if missing_ai:
        print("\n安装AI/ML依赖包...")
        
        # 特殊处理一些包
        for package in missing_ai:
            if package == "torch":
                # PyTorch需要特殊安装命令
                print("安装PyTorch (CPU版本)...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    "torch", "torchvision", "torchaudio", "--index-url", 
                    "https://download.pytorch.org/whl/cpu"
                ])
            elif package == "jaxlib":
                # JAX需要特殊处理
                print("安装JAX (CPU版本)...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    "jax[cpu]"
                ])
            elif package == "rdkit":
                # RDKit需要conda安装
                print("尝试安装RDKit...")
                try:
                    subprocess.check_call([
                        sys.executable, "-m", "pip", "install", "rdkit"
                    ])
                except:
                    print("⚠️  RDKit安装失败，请手动安装: conda install -c conda-forge rdkit")
            else:
                install_package(package)
    
    # 询问是否安装可选包
    if missing_optional:
        print(f"\n🤔 发现 {len(missing_optional)} 个可选依赖包未安装:")
        for package in missing_optional:
            print(f"  - {package}")
        
        response = input("\n是否安装可选依赖包? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            for package in missing_optional:
                install_package(package)
    
    print("\n🔍 重新检查安装结果...")
    
    # 重新检查
    failed_packages = []
    for package, import_name in basic_packages + ai_packages:
        if not check_package(package, import_name):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包或检查网络连接")
    else:
        print("\n🎉 所有依赖包安装完成！")
    
    print("\n📋 安装总结:")
    print("✅ 基础功能: 可用" if not missing_basic else "❌ 基础功能: 部分依赖缺失")
    print("✅ AI功能: 可用" if not missing_ai else "❌ AI功能: 部分依赖缺失")
    
    # 创建环境检查脚本
    create_env_check_script()
    
    print("\n💡 提示:")
    print("1. 运行 'python check_environment.py' 可以随时检查环境")
    print("2. 如果仍有问题，请检查Python版本 (推荐3.8+)")
    print("3. 某些包可能需要重启Python解释器才能生效")

def create_env_check_script():
    """创建环境检查脚本"""
    script_content = '''#!/usr/bin/env python3
"""
环境检查脚本
"""

import sys
import importlib

def check_environment():
    """检查ReaxFFOpt运行环境"""
    print("🔍 ReaxFFOpt 环境检查")
    print("=" * 40)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("⚠️  警告: 推荐使用Python 3.8或更高版本")
    
    # 检查关键包
    packages = [
        ("numpy", "数值计算"),
        ("matplotlib", "图形绘制"),
        ("PyQt5", "GUI界面"),
        ("torch", "深度学习"),
        ("jax", "科学计算"),
        ("transformers", "大语言模型"),
        ("cv2", "计算机视觉"),
        ("networkx", "图网络"),
    ]
    
    available_count = 0
    for package, description in packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package:<15} - {description}")
            available_count += 1
        except ImportError:
            print(f"❌ {package:<15} - {description} (未安装)")
    
    print(f"\\n📊 可用性: {available_count}/{len(packages)} ({available_count/len(packages)*100:.1f}%)")
    
    if available_count == len(packages):
        print("🎉 环境完整，所有功能可用！")
    elif available_count >= len(packages) * 0.7:
        print("✅ 环境基本完整，大部分功能可用")
    else:
        print("⚠️  环境不完整，建议运行 install_dependencies.py")

if __name__ == "__main__":
    check_environment()
'''
    
    with open("check_environment.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ 已创建环境检查脚本: check_environment.py")

if __name__ == "__main__":
    main()
