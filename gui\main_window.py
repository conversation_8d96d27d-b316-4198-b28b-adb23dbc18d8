"""
ReaxFFOpt GUI主窗口
"""

import sys
import os
import json
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTabWidget, QAction, QMenu, QToolBar, QStatusBar, QLabel, 
                            QFileDialog, QMessageBox, QDockWidget, QSplitter, QFrame, QTableWidgetItem, QCheckBox,
                            QDialog, QListWidget, QListWidgetItem, QPushButton)
from PyQt5.QtCore import Qt, pyqtSlot, QThreadPool, QTimer, QThread, QSettings, pyqtSignal
from PyQt5.QtGui import QIcon, QFont

# 导入自定义面板
from gui.parameter_panel import ParameterPanel
from gui.visualization_panel import VisualizationPanel
from gui.molecular_viewer import MolecularViewer
# 注释掉暂时不存在的导入
# from gui.data_panel import DataPanel
# from optimizer.multi_objective import MultiObjectiveOptimizer
# from calculator.reaxff_calculator import ReaxFFCalculator
from data.data_handler import DatasetWorker as DataWorker


class OptimizationWorker(QThread):
    """优化线程，用于在后台执行优化任务"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)
    optimization_finished = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, optimizer, initial_params, training_data):
        """初始化优化线程
        
        Args:
            optimizer: 优化器对象
            initial_params (dict): 初始参数
            training_data (dict): 训练数据
        """
        super().__init__()
        self.optimizer = optimizer
        self.initial_params = initial_params
        self.training_data = training_data
        self.is_running = False
    
    def run(self):
        """执行优化任务"""
        self.is_running = True
        try:
            # 报告开始
            self.progress_updated.emit(0, "开始优化...")
            
            # 模拟优化过程
            import time
            for i in range(1, 101):
                if not self.is_running:
                    break
                time.sleep(0.05)
                self.progress_updated.emit(i, f"优化进度: {i}%")
            
            # 报告完成
            self.progress_updated.emit(100, "优化完成")
            self.optimization_finished.emit([])
            
        except Exception as e:
            self.error_occurred.emit(f"优化过程中出错: {str(e)}")
        
        finally:
            self.is_running = False
    
    def stop(self):
        """停止优化"""
        self.is_running = False


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        
        # 设置基本属性
        self.setWindowTitle("ReaxFFOpt - 先进的反应力场参数优化框架")
        self.setMinimumSize(1200, 800)
        
        # 初始化线程池
        self.thread_pool = QThreadPool()
        
        # 初始化ReaxFF计算器和数据
        # self.calculator = ReaxFFCalculator()  # 暂时注释掉
        self.calculator = None
        self.training_data = {}
        self.current_optimizer = None
        self.optimization_worker = None
        
        # 创建中央窗口部件
        self.init_central_widget()
        
        # 创建菜单和工具栏
        self.create_actions()
        self.create_menus()
        self.create_toolbars()
        
        # 创建状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
        
        # 添加进度指示器
        self.progress_label = QLabel("进度: 0%")
        self.status_bar.addPermanentWidget(self.progress_label)
        
        # 初始化模拟优化定时器
        self.sim_timer = QTimer()
        self.sim_timer.timeout.connect(self.simulate_optimization_update)
        self.sim_iteration = 0
    
    def init_central_widget(self):
        """初始化中央部件"""
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 创建参数面板和可视化面板
        self.parameter_panel = ParameterPanel()
        self.visualization_panel = VisualizationPanel()
        
        # 连接信号
        self.parameter_panel.optimization_started.connect(self.start_optimization)
        self.parameter_panel.stop_button.clicked.connect(self.stop_optimization)
        
        # 添加到分割器
        main_splitter.addWidget(self.parameter_panel)
        main_splitter.addWidget(self.visualization_panel)
        
        # 设置分割器初始大小
        main_splitter.setSizes([400, 800])
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.addWidget(main_splitter)
    
    def create_actions(self):
        """创建动作"""
        # 文件操作
        self.new_project_act = QAction("新建项目", self)
        self.new_project_act.setShortcut("Ctrl+N")
        self.new_project_act.triggered.connect(self.new_project)
        
        self.open_project_act = QAction("打开项目", self)
        self.open_project_act.setShortcut("Ctrl+O")
        self.open_project_act.triggered.connect(self.open_project)
        
        self.save_project_act = QAction("保存项目", self)
        self.save_project_act.setShortcut("Ctrl+S")
        self.save_project_act.triggered.connect(self.save_project)
        
        self.exit_act = QAction("退出", self)
        self.exit_act.setShortcut("Alt+F4")
        self.exit_act.triggered.connect(self.close)
        
        # 导入操作
        self.import_ffield_act = QAction("导入力场文件", self)
        self.import_ffield_act.triggered.connect(self.import_ffield)
        
        self.import_structure_act = QAction("导入分子结构", self)
        self.import_structure_act.triggered.connect(self.import_structure)
        
        self.import_training_set_act = QAction("导入训练集", self)
        self.import_training_set_act.triggered.connect(self.import_training_set)
        
        self.import_dataset_folder_act = QAction("导入数据集文件夹", self)
        self.import_dataset_folder_act.triggered.connect(self.import_dataset_folder)
        
        # 优化操作
        self.start_opt_act = QAction("开始优化", self)
        self.start_opt_act.setShortcut("F5")
        self.start_opt_act.triggered.connect(lambda: self.parameter_panel.start_button.click())
        
        self.stop_opt_act = QAction("停止优化", self)
        self.stop_opt_act.setShortcut("Esc")
        self.stop_opt_act.setEnabled(False)
        self.stop_opt_act.triggered.connect(self.stop_optimization)
        
        # 新增：多目标优化操作
        self.multi_objective_opt_act = QAction("多目标优化", self)
        self.multi_objective_opt_act.triggered.connect(self.run_multi_objective_optimization)
        
        self.multi_fidelity_opt_act = QAction("多保真度优化", self)
        self.multi_fidelity_opt_act.triggered.connect(self.run_multi_fidelity_optimization)
        
        self.plot_pareto_front_act = QAction("绘制帕累托前沿", self)
        self.plot_pareto_front_act.triggered.connect(self.plot_pareto_front)
        self.plot_pareto_front_act.setEnabled(False)
        
        # 工具操作
        self.parameter_sensitivity_act = QAction("参数敏感性分析", self)
        self.parameter_sensitivity_act.triggered.connect(self.run_sensitivity_analysis)
        
        self.generate_ai_parameters_act = QAction("生成式AI参数预测", self)
        self.generate_ai_parameters_act.triggered.connect(self.run_ai_generation)
        
        self.quantum_optimization_act = QAction("量子优化模式", self)
        self.quantum_optimization_act.setCheckable(True)
        
        # 帮助操作
        self.about_act = QAction("关于", self)
        self.about_act.triggered.connect(self.show_about)
        
        self.help_act = QAction("帮助文档", self)
        self.help_act.triggered.connect(self.show_help)
    
    def create_menus(self):
        """创建菜单"""
        # 文件菜单
        self.file_menu = self.menuBar().addMenu("文件")
        self.file_menu.addAction(self.new_project_act)
        self.file_menu.addAction(self.open_project_act)
        self.file_menu.addAction(self.save_project_act)
        self.file_menu.addSeparator()
        
        # 导入子菜单
        self.import_menu = self.file_menu.addMenu("导入")
        self.import_menu.addAction(self.import_ffield_act)
        self.import_menu.addAction(self.import_structure_act)
        self.import_menu.addAction(self.import_training_set_act)
        self.import_menu.addAction(self.import_dataset_folder_act)
        
        # 添加保存菜单
        self.file_menu.addSeparator()  # 添加分隔线
        self.save_menu = self.file_menu.addMenu("保存")
        
        self.save_ffield = QAction("保存力场文件...", self)
        self.save_ffield.setShortcut("Ctrl+S")
        self.save_ffield.setStatusTip("将优化后的力场文件保存到指定位置")
        self.save_ffield.triggered.connect(self.save_force_field)
        self.save_menu.addAction(self.save_ffield)
        
        # 添加退出动作
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.exit_act)
        
        # 优化菜单
        self.optimization_menu = self.menuBar().addMenu("优化")
        self.optimization_menu.addAction(self.start_opt_act)
        self.optimization_menu.addAction(self.stop_opt_act)
        self.optimization_menu.addSeparator()
        
        # 新增：多目标优化子菜单
        self.multi_opt_menu = self.optimization_menu.addMenu("多目标优化")
        self.multi_opt_menu.addAction(self.multi_objective_opt_act)
        self.multi_opt_menu.addAction(self.multi_fidelity_opt_act)
        self.multi_opt_menu.addSeparator()
        self.multi_opt_menu.addAction(self.plot_pareto_front_act)
        
        # 工具菜单
        self.tools_menu = self.menuBar().addMenu("工具")
        self.tools_menu.addAction(self.parameter_sensitivity_act)
        self.tools_menu.addAction(self.generate_ai_parameters_act)
        self.tools_menu.addAction(self.quantum_optimization_act)
        
        # 视图菜单
        self.view_menu = self.menuBar().addMenu("视图")
        
        # 帮助菜单
        self.help_menu = self.menuBar().addMenu("帮助")
        self.help_menu.addAction(self.about_act)
        self.help_menu.addAction(self.help_act)
    
    def create_toolbars(self):
        """创建工具栏"""
        # 文件工具栏
        file_toolbar = self.addToolBar("文件")
        file_toolbar.addAction(self.new_project_act)
        file_toolbar.addAction(self.open_project_act)
        file_toolbar.addAction(self.save_project_act)
        
        # 优化工具栏
        optimization_toolbar = self.addToolBar("优化")
        optimization_toolbar.addAction(self.start_opt_act)
        optimization_toolbar.addAction(self.stop_opt_act)
        optimization_toolbar.addAction(self.parameter_sensitivity_act)
    
    def new_project(self):
        """创建新项目"""
        reply = QMessageBox.question(
            self, "新项目确认",
            "创建新项目将清除当前所有数据。是否继续？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 清除当前数据
            self.status_bar.showMessage("已创建新项目")
    
    def open_project(self):
        """打开项目"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目", "", "ReaxFFOpt项目文件 (*.roxp);;所有文件 (*.*)"
        )
        
        if file_path:
            self.status_bar.showMessage(f"已打开项目: {os.path.basename(file_path)}")
    
    def save_project(self):
        """保存项目"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存项目", "", "ReaxFFOpt项目文件 (*.roxp);;所有文件 (*.*)"
        )
        
        if file_path:
            if not file_path.endswith('.roxp'):
                file_path += '.roxp'
            self.status_bar.showMessage(f"已保存项目: {os.path.basename(file_path)}")
    
    def import_ffield(self):
        """导入力场文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入力场文件", "", "ReaxFF力场文件 (*.ffield);;所有文件 (*.*)"
        )
        
        if file_path:
            self.status_bar.showMessage(f"已导入力场文件: {os.path.basename(file_path)}")
    
    def import_structure(self):
        """导入分子结构"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入分子结构", "", 
            "结构文件 (*.xyz *.pdb *.mol *.cif);;所有文件 (*.*)"
        )
        
        if file_path:
            self.status_bar.showMessage(f"已导入分子结构: {os.path.basename(file_path)}")
    
    def import_training_set(self):
        """导入训练集"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入训练集", "", "训练集文件 (*.geo);;所有文件 (*.*)"
        )
        
        if file_path:
            self.status_bar.showMessage(f"已导入训练集: {os.path.basename(file_path)}")
    
    def import_dataset_folder(self):
        """导入整个数据集文件夹"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "选择数据集文件夹", ""
        )
        
        if folder_path:
            print(f"选择的数据集文件夹: {folder_path}")
            # 扫描文件夹中的所有数据集
            datasets = self._scan_datasets(folder_path)
            
            if datasets:
                # 显示数据集选择对话框
                dialog = DatasetSelectionDialog(datasets, self)
                if dialog.exec_() == QDialog.Accepted:
                    selected_datasets = dialog.get_selected_datasets()
                    
                    if selected_datasets:
                        self.status_bar.showMessage(f"正在处理选中的数据集")
                        self.progress_label.setText("进度: 0%")
                        
                        # 创建数据处理线程
                        self.data_worker = DataWorker(folder_path, selected_datasets)
                        
                        # 连接信号
                        self.data_worker.progress.connect(self.update_dataset_progress)
                        self.data_worker.finished.connect(self.dataset_import_finished)
                        
                        # 启动线程
                        self.data_worker.start()
                    else:
                        QMessageBox.warning(self, "警告", "请至少选择一个数据集")
            else:
                QMessageBox.warning(self, "警告", "所选文件夹中没有找到有效的数据集")
    
    def _scan_datasets(self, folder_path):
        """扫描文件夹中的数据集
        
        Args:
            folder_path (str): 文件夹路径
            
        Returns:
            list: 数据集列表
        """
        datasets = []
        print(f"开始扫描数据集文件夹: {folder_path}")
        
        try:
            # 遍历顶层目录
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path):
                    print(f"检查目录: {item}")
                    # 检查是否包含必要的文件
                    files = os.listdir(item_path)
                    files_lower = [f.lower() for f in files]
                    print(f"目录 {item} 包含文件: {files}")
                    
                    # 检查必要文件
                    has_geo = any(f == 'geo' for f in files)
                    has_params = any(f == 'params' for f in files)
                    has_ffield = any(f.startswith('ffield') for f in files)
                    has_trainset = any(f == 'trainset.in' for f in files)
                    
                    print(f"目录 {item} 文件检查结果:")
                    print(f"- geo文件: {'存在' if has_geo else '不存在'}")
                    print(f"- params文件: {'存在' if has_params else '不存在'}")
                    print(f"- ffield文件: {'存在' if has_ffield else '不存在'}")
                    print(f"- trainset文件: {'存在' if has_trainset else '不存在'}")
                    
                    # 如果包含必要文件，添加到数据集列表
                    if has_geo and (has_params or has_ffield or has_trainset):
                        datasets.append(item)
                        print(f"找到有效数据集: {item}")
                        
        except Exception as e:
            print(f"扫描数据集时出错: {str(e)}")
        
        if not datasets:
            print("警告: 未找到任何有效的数据集")
        else:
            print(f"共找到 {len(datasets)} 个有效数据集: {', '.join(datasets)}")
            
        return datasets
    
    def update_dataset_progress(self, value):
        """更新数据集导入进度"""
        self.progress_label.setText(f"进度: {value}%")
    
    def dataset_import_finished(self, result):
        """数据集导入完成处理"""
        # 显示导入结果
        message = f"数据集导入完成：\n"
        message += f"- 结构文件: {result['structures']}个\n"
        message += f"- 能量数据: {result['energies']}个\n"
        message += f"- 力数据: {result['forces']}个\n"
        message += f"- 训练集: {result['training_sets']}个"
        if 'parameters' in result:
            message += f"\n- 参数: {result['parameters']}个"
        
        QMessageBox.information(self, "数据集导入", message)
        
        # 更新界面上的相关组件
        self.status_bar.showMessage(f"数据集导入完成")
        
        # 如果有参数面板，更新它
        if hasattr(self, 'parameter_panel'):
            # 更新训练集文件路径
            self.parameter_panel.config_panel.training_file_edit.setText(
                os.path.join(self.data_worker.folder_path, "trainset.in")
            )
            
            # 更新参数表格
            parameters = self.data_worker.handler.get_all_parameters()
            if parameters:
                # 获取第一个数据集的参数
                first_dataset_params = next(iter(parameters.values()))
                # 清空现有参数表格
                self.parameter_panel.parameter_table.setRowCount(0)
                # 添加新参数
                for param_name, param_info in first_dataset_params.items():
                    row = self.parameter_panel.parameter_table.rowCount()
                    self.parameter_panel.parameter_table.insertRow(row)
                    
                    # 参数名
                    self.parameter_panel.parameter_table.setItem(row, 0, QTableWidgetItem(param_name))
                    
                    # 当前值
                    value_item = QTableWidgetItem(f"{param_info['value']:.4f}")
                    value_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    self.parameter_panel.parameter_table.setItem(row, 1, value_item)
                    
                    # 范围
                    range_text = f"{param_info['min']:.1f} - {param_info['max']:.1f}"
                    range_item = QTableWidgetItem(range_text)
                    range_item.setTextAlignment(Qt.AlignCenter)
                    self.parameter_panel.parameter_table.setItem(row, 2, range_item)
                    
                    # 优化复选框
                    checkbox = QCheckBox()
                    checkbox.setChecked(True)  # 默认选中
                    checkbox_widget = QWidget()
                    checkbox_layout = QHBoxLayout(checkbox_widget)
                    checkbox_layout.addWidget(checkbox)
                    checkbox_layout.setAlignment(Qt.AlignCenter)
                    checkbox_layout.setContentsMargins(0, 0, 0, 0)
                    self.parameter_panel.parameter_table.setCellWidget(row, 3, checkbox_widget)
                    
                    # 敏感性（初始为0）
                    sensitivity_item = QTableWidgetItem("0.00")
                    sensitivity_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    self.parameter_panel.parameter_table.setItem(row, 4, sensitivity_item)
        
        # 如果有可视化面板，更新它
        if hasattr(self, 'visualization_panel'):
            # 更新结构列表
            structures = self.data_worker.handler.get_all_structures()
            if structures:
                self.visualization_panel.update_structures(structures)
                
        # 清理线程
        self.data_worker.quit()
        self.data_worker.wait()
        self.data_worker = None
    
    @pyqtSlot(dict)
    def start_optimization(self, config):
        """开始优化过程"""
        self.status_bar.showMessage(f"正在运行优化: {config['method']}")
        self.stop_opt_act.setEnabled(True)
        
        # 获取选中的参数
        parameters = config['parameters']
        if not parameters:
            QMessageBox.warning(self, "参数错误", "没有选择要优化的参数")
            return
            
        # 创建优化器
        try:
            from optimizer.optimizer import create_optimizer
            from optimizer.objectives import create_objective_function
            
            # 创建目标函数
            objective = create_objective_function(
                training_data=self.data_worker.handler.get_all_training_sets(),
                weight_scheme=config.get('weight_scheme', 'default')
            )
            
            # 创建优化器
            self.optimizer = create_optimizer(
                method=config['method'],
                parameters=parameters,
                objective=objective,
                population_size=config.get('population_size', 50),
                max_iterations=config.get('max_iterations', 200),
                tolerance=config.get('tolerance', 1e-5),
                use_quantum=config.get('use_quantum', False),
                use_ai_model=config.get('use_ai_model', False),
                parallel_threads=config.get('parallel_threads', 4)
            )
            
            # 连接优化器信号
            self.optimizer.iteration_finished.connect(self.update_optimization_progress)
            self.optimizer.optimization_finished.connect(self.optimization_finished)
            
            # 启动优化
            self.optimizer.start()
            
        except Exception as e:
            QMessageBox.critical(self, "优化错误", f"启动优化失败：\n{str(e)}")
            self.stop_optimization()
    
    def update_optimization_progress(self, data):
        """更新优化进度
        
        Args:
            data (dict): 包含优化进度数据的字典
                - iteration: 当前迭代次数
                - best_value: 当前最优值
                - population: 当前种群
                - search_path: 搜索路径
        """
        # 更新进度条
        progress = int((data['iteration'] / self.optimizer.max_iterations) * 100)
        self.progress_label.setText(f"进度: {progress}%")
        
        # 更新优化进程图
        self.visualization_panel.update_optimization_plot(
            data['iterations'],
            data['best_values']
        )
        
        # 更新参数空间图
        if 'search_path' in data:
            self.visualization_panel.update_parameter_space(
                data['search_path'],
                data['current_best']
            )
        
        # 如果是多目标优化，更新帕累托前沿
        if 'pareto_front' in data:
            self.visualization_panel.update_pareto_front(data['pareto_front'])
    
    def optimization_finished(self, result):
        """优化完成处理
        
        Args:
            result (dict): 优化结果
                - best_parameters: 最优参数值
                - best_value: 最优目标函数值
                - convergence_history: 收敛历史
                - search_path: 完整搜索路径
                - pareto_front: 帕累托前沿（如果是多目标优化）
        """
        # 显示优化结果
        message = f"优化完成：\n"
        message += f"最优目标函数值: {result['best_value']:.6f}\n"
        message += "\n最优参数值:\n"
        for param_name, value in result['best_parameters'].items():
            message += f"{param_name}: {value:.6f}\n"
        
        QMessageBox.information(self, "优化完成", message)
        
        # 更新参数面板中的参数值
        self.parameter_panel.update_parameters(result['best_parameters'])
        
        # 保存优化结果
        self.save_optimization_result(result)
        
        # 更新按钮状态
        self.stop_opt_act.setEnabled(False)
        self.status_bar.showMessage("优化完成")
    
    def save_optimization_result(self, result):
        """保存优化结果
        
        Args:
            result (dict): 优化结果
        """
        try:
            # 创建时间戳文件夹
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_dir = os.path.join(self.data_worker.folder_path, f"optimization_results_{timestamp}")
            os.makedirs(result_dir, exist_ok=True)
            
            # 保存最优力场文件
            ffield_path = os.path.join(result_dir, "ffield_best")
            self.save_force_field_file(ffield_path, result['best_parameters'])
            
            # 保存优化历史数据
            history_path = os.path.join(result_dir, "optimization_history.json")
            with open(history_path, 'w') as f:
                json.dump({
                    'convergence_history': result['convergence_history'],
                    'search_path': result['search_path'],
                    'pareto_front': result.get('pareto_front', None)
                }, f, indent=2)
            
            # 保存图表
            self.visualization_panel.save_all_figures(result_dir)
            
            self.status_bar.showMessage(f"优化结果已保存到: {result_dir}")
            
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存优化结果时出错：\n{str(e)}")
    
    def save_force_field_file(self, filepath, parameters):
        """保存力场文件
        
        Args:
            filepath (str): 保存路径
            parameters (dict): 参数值字典
        """
        try:
            from force_field.writer import write_force_field
            write_force_field(filepath, parameters)
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存力场文件时出错：\n{str(e)}")
    
    def stop_optimization(self):
        """停止优化过程"""
        self.sim_timer.stop()
        self.status_bar.showMessage("优化已停止")
        self.stop_opt_act.setEnabled(False)
        
        # 更新参数面板按钮状态
        self.parameter_panel.start_button.setEnabled(True)
        self.parameter_panel.stop_button.setEnabled(False)
    
    def simulate_optimization_update(self):
        """模拟优化过程更新"""
        self.sim_iteration += 1
        
        # 更新进度显示
        progress = min(100, int(self.sim_iteration / 50 * 100))
        self.progress_label.setText(f"进度: {progress}%")
        
        # 更新可视化
        import numpy as np
        
        # 生成模拟数据
        iterations = np.arange(self.sim_iteration)
        values = 100 * np.exp(-0.05 * iterations) + 10 * np.exp(-0.1 * iterations) * np.sin(0.5 * iterations) + 2
        
        # 更新优化可视化
        self.visualization_panel.update_optimization_plot(iterations, values)
        
        # 当达到一定迭代次数时停止
        if self.sim_iteration >= 50:
            self.stop_optimization()
            self.status_bar.showMessage("优化完成")
    
    def run_sensitivity_analysis(self):
        """运行参数敏感性分析"""
        self.status_bar.showMessage("正在运行参数敏感性分析...")
        
        # 模拟生成一些敏感性数据
        import numpy as np
        
        sensitivities = {
            "p_val1": 0.85,
            "p_val2": 0.62,
            "p_bond1": 0.31,
            "p_angle1": 0.18,
            "p_tors1": 0.09,
            "p_hbond1": 0.05
        }
        
        # 更新参数面板
        self.parameter_panel.set_parameter_sensitivities(sensitivities)
        self.status_bar.showMessage("参数敏感性分析完成")
    
    def run_ai_generation(self):
        """运行生成式AI参数预测"""
        self.status_bar.showMessage("正在运行生成式AI参数预测...")
        
        # 在实际应用中，这里会调用AI模型进行参数生成
        QTimer.singleShot(2000, lambda: self.status_bar.showMessage("生成式AI参数预测完成"))
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, 
            "关于ReaxFFOpt",
            "ReaxFFOpt - 先进的反应力场参数优化框架\n\n"
            "版本: 1.0.0\n"
            "开发团队: ReaxFFOpt团队\n\n"
            "基于多模态科学大模型、高效搜索路径和量子计算协同优化"
        )
    
    def show_help(self):
        """显示帮助文档"""
        QMessageBox.information(
            self,
            "帮助文档",
            "帮助文档尚未实现。请访问项目主页获取更多信息。"
        )
    
    def run_multi_objective_optimization(self):
        """运行多目标优化"""
        # 检查训练数据
        if not hasattr(self, 'training_data') or not self.training_data:
            QMessageBox.warning(self, "警告", "请先导入训练数据")
            return
        
        # 显示未实现提示
        QMessageBox.information(self, "提示", "多目标优化功能尚未完全实现")
        
        # 模拟一些数据用于测试
        import numpy as np
        
        # 模拟帕累托前沿数据
        pareto_points = []
        for _ in range(10):
            x = 5 + 10 * np.random.random()
            y = 50 / x + 1 * np.random.random()
            pareto_points.append((x, y))
        
        # 显示帕累托前沿
        self.plot_pareto_front(pareto_points)
        
        # 启用帕累托前沿按钮
        self.plot_pareto_front_act.setEnabled(True)
    
    def run_multi_fidelity_optimization(self):
        """运行多保真度优化"""
        # 检查训练数据
        if not hasattr(self, 'training_data') or not self.training_data:
            QMessageBox.warning(self, "警告", "请先导入训练数据")
            return
        
        # 显示未实现提示
        QMessageBox.information(self, "提示", "多保真度优化功能尚未完全实现")
    
    def plot_pareto_front(self, points=None):
        """绘制帕累托前沿
        
        Args:
            points (list, optional): 帕累托前沿点，每个点为(x,y)元组
        """
        if points is None and (not hasattr(self, 'pareto_points') or not self.pareto_points):
            QMessageBox.warning(self, "警告", "没有可用的帕累托前沿数据")
            return
        
        if points is not None:
            self.pareto_points = points
        
        # 创建图形
        import matplotlib.pyplot as plt
        import numpy as np
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 绘制帕累托前沿
        x = [p[0] for p in self.pareto_points]
        y = [p[1] for p in self.pareto_points]
        
        # 散点图
        ax.scatter(x, y, c='red', s=100, label='帕累托最优解')
        
        # 按x排序并连线
        sorted_indices = np.argsort(x)
        sorted_x = [x[i] for i in sorted_indices]
        sorted_y = [y[i] for i in sorted_indices]
        ax.plot(sorted_x, sorted_y, 'r--')
        
        ax.set_xlabel('目标1: 能量RMSE (kcal/mol)')
        ax.set_ylabel('目标2: 力RMSE (kcal/mol/Å)')
        ax.set_title('多目标优化帕累托前沿')
        ax.legend()
        
        # 更新可视化面板
        self.visualization_panel.display_figure(fig)

    def save_force_field(self):
        """保存力场文件"""
        # 打开文件对话框选择保存位置
        save_dir = QFileDialog.getExistingDirectory(
            self, "选择保存目录", "",
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )
        
        if save_dir:
            try:
                # 创建时间戳文件夹
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_dir = os.path.join(save_dir, f"force_fields_{timestamp}")
                os.makedirs(result_dir, exist_ok=True)
                
                # 为每个数据集创建子文件夹
                if hasattr(self, 'data_worker') and self.data_worker:
                    for dataset_name in self.data_worker.handler.structures.keys():
                        dataset_dir = os.path.join(result_dir, dataset_name)
                        os.makedirs(dataset_dir, exist_ok=True)
                        
                        # 复制或生成力场文件
                        ffield_path = os.path.join(dataset_dir, "ffield_best")
                        # TODO: 这里添加实际的力场文件生成逻辑
                        with open(ffield_path, 'w') as f:
                            f.write("# Generated force field file\n")
                            f.write(f"# Dataset: {dataset_name}\n")
                            f.write(f"# Generated at: {timestamp}\n")
                
                QMessageBox.information(self, "保存成功", f"力场文件已保存到:\n{result_dir}")
                
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存力场文件时发生错误:\n{str(e)}")


class DatasetSelectionDialog(QDialog):
    """数据集选择对话框"""
    
    def __init__(self, datasets, parent=None):
        super().__init__(parent)
        self.datasets = datasets
        self.selected_datasets = []
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("选择数据集")
        self.setModal(True)
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 创建列表控件
        self.list_widget = QListWidget()
        for dataset in self.datasets:
            item = QListWidgetItem(dataset)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(Qt.Unchecked)
            self.list_widget.addItem(item)
            
        layout.addWidget(self.list_widget)
        
        # 创建按钮
        button_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all)
        clear_btn = QPushButton("清除")
        clear_btn.clicked.connect(self.clear_selection)
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(clear_btn)
        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def select_all(self):
        """全选"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            item.setCheckState(Qt.Checked)
            
    def clear_selection(self):
        """清除选择"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            item.setCheckState(Qt.Unchecked)
            
    def get_selected_datasets(self):
        """获取选中的数据集
        
        Returns:
            list: 选中的数据集列表
        """
        selected = []
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item.checkState() == Qt.Checked:
                selected.append(item.text())
        return selected


if __name__ == "__main__":
    # 支持高DPI显示
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_()) 