"""
计算机视觉模块 - CNN轨迹分析和AR交互式分析
"""

import numpy as np
import cv2
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Union
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
import logging

logger = logging.getLogger(__name__)


class TrajectoryAnalysisCNN(nn.Module):
    """CNN轨迹分析网络"""
    
    def __init__(self, input_channels: int = 3, num_classes: int = 5):
        super().__init__()
        
        # 3D卷积层用于分析时空轨迹
        self.conv3d_1 = nn.Conv3d(input_channels, 32, kernel_size=3, padding=1)
        self.conv3d_2 = nn.Conv3d(32, 64, kernel_size=3, padding=1)
        self.conv3d_3 = nn.Conv3d(64, 128, kernel_size=3, padding=1)
        
        # 池化层
        self.pool = nn.MaxPool3d(2)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(128, 8)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.AdaptiveAvgPool3d(1),
            nn.Flatten(),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, num_classes)
        )
        
        # 回归头（预测关键参数）
        self.regressor = nn.Sequential(
            nn.AdaptiveAvgPool3d(1),
            nn.Flatten(),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 10)  # 预测10个关键参数
        )
        
    def forward(self, x):
        """前向传播
        
        Args:
            x: 输入轨迹 [batch, channels, time, height, width]
        """
        # 3D卷积特征提取
        x = F.relu(self.conv3d_1(x))
        x = self.pool(x)
        
        x = F.relu(self.conv3d_2(x))
        x = self.pool(x)
        
        x = F.relu(self.conv3d_3(x))
        features = self.pool(x)
        
        # 分类和回归
        classification = self.classifier(features)
        regression = self.regressor(features)
        
        return {
            'classification': classification,
            'regression': regression,
            'features': features
        }


class MolecularTrajectoryAnalyzer:
    """分子轨迹分析器"""
    
    def __init__(self, model_path: Optional[str] = None):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = TrajectoryAnalysisCNN()
        self.model.to(self.device)
        
        if model_path:
            self.load_model(model_path)
            
        self.trajectory_classes = [
            'stable_equilibrium',
            'transition_state',
            'dissociation',
            'association',
            'rearrangement'
        ]
        
    def load_model(self, model_path: str):
        """加载预训练模型"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            logger.info(f"成功加载模型: {model_path}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            
    def preprocess_trajectory(self, trajectory: np.ndarray) -> torch.Tensor:
        """预处理轨迹数据
        
        Args:
            trajectory: 原子轨迹 [time_steps, n_atoms, 3]
            
        Returns:
            预处理后的张量 [1, channels, time, height, width]
        """
        # 将3D轨迹投影到2D图像序列
        time_steps, n_atoms, _ = trajectory.shape
        
        # 使用PCA降维到2D
        pca = PCA(n_components=2)
        trajectory_2d = pca.fit_transform(trajectory.reshape(-1, 3))
        trajectory_2d = trajectory_2d.reshape(time_steps, n_atoms, 2)
        
        # 创建图像序列
        image_size = 64
        images = []
        
        for t in range(time_steps):
            # 创建空白图像
            img = np.zeros((image_size, image_size, 3))
            
            # 归一化坐标到图像范围
            coords = trajectory_2d[t]
            coords_norm = ((coords - coords.min()) / 
                          (coords.max() - coords.min() + 1e-8) * (image_size - 1))
            
            # 绘制原子
            for i, (x, y) in enumerate(coords_norm):
                x, y = int(x), int(y)
                if 0 <= x < image_size and 0 <= y < image_size:
                    # 不同原子类型用不同颜色
                    color = plt.cm.tab10(i % 10)[:3]
                    cv2.circle(img, (x, y), 2, color, -1)
                    
            images.append(img)
            
        # 转换为张量
        images = np.array(images)  # [time, height, width, channels]
        images = images.transpose(3, 0, 1, 2)  # [channels, time, height, width]
        images = torch.FloatTensor(images).unsqueeze(0)  # [1, channels, time, height, width]
        
        return images
        
    def analyze_trajectory(self, trajectory: np.ndarray) -> Dict:
        """分析分子轨迹
        
        Args:
            trajectory: 分子轨迹数据
            
        Returns:
            分析结果
        """
        self.model.eval()
        
        # 预处理
        input_tensor = self.preprocess_trajectory(trajectory).to(self.device)
        
        with torch.no_grad():
            output = self.model(input_tensor)
            
        # 解析结果
        classification = F.softmax(output['classification'], dim=1)
        predicted_class = torch.argmax(classification, dim=1).item()
        confidence = classification[0, predicted_class].item()
        
        regression_params = output['regression'][0].cpu().numpy()
        
        return {
            'trajectory_type': self.trajectory_classes[predicted_class],
            'confidence': confidence,
            'predicted_parameters': regression_params,
            'class_probabilities': classification[0].cpu().numpy(),
            'features': output['features'][0].cpu().numpy()
        }
        
    def detect_critical_events(self, trajectory: np.ndarray) -> List[Dict]:
        """检测轨迹中的关键事件"""
        events = []
        
        # 计算能量变化
        energies = self._calculate_potential_energy(trajectory)
        
        # 检测能量峰值（可能的过渡态）
        peaks = self._find_peaks(energies)
        for peak in peaks:
            events.append({
                'type': 'transition_state',
                'time': peak,
                'energy': energies[peak],
                'confidence': 0.8
            })
            
        # 检测键长变化
        bond_changes = self._detect_bond_changes(trajectory)
        for change in bond_changes:
            events.append({
                'type': 'bond_breaking' if change['delta'] > 0 else 'bond_forming',
                'time': change['time'],
                'atoms': change['atoms'],
                'confidence': 0.7
            })
            
        return events
        
    def _calculate_potential_energy(self, trajectory: np.ndarray) -> np.ndarray:
        """计算势能（简化版）"""
        energies = []
        for t in range(len(trajectory)):
            coords = trajectory[t]
            # 简化的势能计算：基于原子间距离
            energy = 0
            for i in range(len(coords)):
                for j in range(i + 1, len(coords)):
                    r = np.linalg.norm(coords[i] - coords[j])
                    # Lennard-Jones势能
                    energy += 4 * ((1/r)**12 - (1/r)**6)
            energies.append(energy)
        return np.array(energies)
        
    def _find_peaks(self, signal: np.ndarray, threshold: float = 0.1) -> List[int]:
        """寻找信号峰值"""
        peaks = []
        for i in range(1, len(signal) - 1):
            if (signal[i] > signal[i-1] and signal[i] > signal[i+1] and 
                signal[i] > threshold):
                peaks.append(i)
        return peaks
        
    def _detect_bond_changes(self, trajectory: np.ndarray) -> List[Dict]:
        """检测键长变化"""
        changes = []
        n_atoms = len(trajectory[0])
        
        # 计算所有原子对的距离变化
        for i in range(n_atoms):
            for j in range(i + 1, n_atoms):
                distances = []
                for t in range(len(trajectory)):
                    dist = np.linalg.norm(trajectory[t][i] - trajectory[t][j])
                    distances.append(dist)
                    
                distances = np.array(distances)
                
                # 检测显著变化
                if len(distances) > 10:
                    initial = np.mean(distances[:5])
                    final = np.mean(distances[-5:])
                    delta = final - initial
                    
                    if abs(delta) > 0.5:  # 阈值
                        # 找到变化发生的时间
                        change_time = np.argmax(np.abs(np.diff(distances)))
                        changes.append({
                            'atoms': (i, j),
                            'time': change_time,
                            'delta': delta,
                            'initial_distance': initial,
                            'final_distance': final
                        })
                        
        return changes


class ARVisualizationEngine:
    """AR交互式分析引擎"""
    
    def __init__(self):
        self.camera_matrix = None
        self.dist_coeffs = None
        self.aruco_dict = cv2.aruco.Dictionary_get(cv2.aruco.DICT_6X6_250)
        self.aruco_params = cv2.aruco.DetectorParameters_create()
        
    def calibrate_camera(self, calibration_images: List[np.ndarray]) -> bool:
        """相机标定"""
        try:
            # 棋盘格参数
            pattern_size = (9, 6)
            square_size = 1.0
            
            # 准备对象点
            objp = np.zeros((pattern_size[0] * pattern_size[1], 3), np.float32)
            objp[:, :2] = np.mgrid[0:pattern_size[0], 0:pattern_size[1]].T.reshape(-1, 2)
            objp *= square_size
            
            # 存储点
            objpoints = []
            imgpoints = []
            
            for img in calibration_images:
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                ret, corners = cv2.findChessboardCorners(gray, pattern_size, None)
                
                if ret:
                    objpoints.append(objp)
                    imgpoints.append(corners)
                    
            if len(objpoints) > 0:
                ret, self.camera_matrix, self.dist_coeffs, _, _ = cv2.calibrateCamera(
                    objpoints, imgpoints, gray.shape[::-1], None, None
                )
                return ret
            return False
            
        except Exception as e:
            logger.error(f"相机标定失败: {e}")
            return False
            
    def render_molecular_ar(self, frame: np.ndarray, 
                           molecule_coords: np.ndarray,
                           marker_id: int = 0) -> np.ndarray:
        """在AR中渲染分子结构"""
        if self.camera_matrix is None:
            return frame
            
        # 检测ArUco标记
        corners, ids, _ = cv2.aruco.detectMarkers(
            frame, self.aruco_dict, parameters=self.aruco_params
        )
        
        if ids is not None and marker_id in ids:
            # 获取标记位置
            marker_idx = np.where(ids == marker_id)[0][0]
            marker_corners = corners[marker_idx][0]
            
            # 估计姿态
            rvec, tvec, _ = cv2.aruco.estimatePoseSingleMarkers(
                [marker_corners], 0.05, self.camera_matrix, self.dist_coeffs
            )
            
            # 绘制坐标轴
            cv2.aruco.drawAxis(frame, self.camera_matrix, self.dist_coeffs, 
                             rvec, tvec, 0.03)
            
            # 投影分子坐标到图像
            molecule_3d = np.array(molecule_coords, dtype=np.float32)
            molecule_3d = molecule_3d.reshape(-1, 1, 3) * 0.01  # 缩放
            
            projected_points, _ = cv2.projectPoints(
                molecule_3d, rvec, tvec, self.camera_matrix, self.dist_coeffs
            )
            
            # 绘制原子
            for i, point in enumerate(projected_points):
                x, y = point[0].astype(int)
                color = (0, 255, 0) if i % 2 == 0 else (0, 0, 255)
                cv2.circle(frame, (x, y), 5, color, -1)
                
            # 绘制键
            for i in range(len(projected_points) - 1):
                pt1 = tuple(projected_points[i][0].astype(int))
                pt2 = tuple(projected_points[i+1][0].astype(int))
                cv2.line(frame, pt1, pt2, (255, 255, 255), 2)
                
        return frame
        
    def create_interactive_session(self, trajectory: np.ndarray) -> Dict:
        """创建交互式AR会话"""
        session = {
            'trajectory': trajectory,
            'current_frame': 0,
            'playing': False,
            'speed': 1.0,
            'annotations': [],
            'measurements': []
        }
        return session
