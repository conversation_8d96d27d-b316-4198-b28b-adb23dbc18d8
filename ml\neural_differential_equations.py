"""
神经微分方程(NDE)模块 - 连续时空力场建模
"""

import jax
import jax.numpy as jnp
from jax import grad, jit, vmap
from jax.experimental.ode import odeint
import haiku as hk
import numpy as np
from typing import Dict, List, Tuple, Optional, Callable
import optax
from functools import partial
import logging

logger = logging.getLogger(__name__)


class NeuralODE(hk.Module):
    """神经常微分方程网络"""
    
    def __init__(self, hidden_dims: List[int] = [64, 64], name: str = "neural_ode"):
        super().__init__(name=name)
        self.hidden_dims = hidden_dims
        
    def __call__(self, t: jnp.ndarray, y: jnp.ndarray) -> jnp.ndarray:
        """ODE函数 dy/dt = f(t, y)
        
        Args:
            t: 时间
            y: 状态向量
            
        Returns:
            状态导数
        """
        # 将时间和状态拼接作为输入
        x = jnp.concatenate([jnp.array([t]), y])
        
        # 神经网络
        for i, dim in enumerate(self.hidden_dims):
            x = hk.Linear(dim)(x)
            if i < len(self.hidden_dims) - 1:
                x = jax.nn.tanh(x)
                
        # 输出维度与状态向量相同
        x = hk.Linear(y.shape[0])(x)
        return x


class ContinuousForceField(hk.Module):
    """连续时空力场模型"""
    
    def __init__(self, 
                 n_atoms: int,
                 spatial_dims: int = 3,
                 hidden_dims: List[int] = [128, 64, 32],
                 name: str = "continuous_forcefield"):
        super().__init__(name=name)
        self.n_atoms = n_atoms
        self.spatial_dims = spatial_dims
        self.hidden_dims = hidden_dims
        
    def __call__(self, t: jnp.ndarray, positions: jnp.ndarray) -> jnp.ndarray:
        """计算连续力场
        
        Args:
            t: 时间
            positions: 原子位置 [n_atoms, spatial_dims]
            
        Returns:
            力向量 [n_atoms, spatial_dims]
        """
        # 展平位置向量
        pos_flat = positions.flatten()
        
        # 时空特征
        features = jnp.concatenate([jnp.array([t]), pos_flat])
        
        # 多层感知机
        x = features
        for dim in self.hidden_dims:
            x = hk.Linear(dim)(x)
            x = jax.nn.swish(x)
            
        # 输出力向量
        forces_flat = hk.Linear(self.n_atoms * self.spatial_dims)(x)
        forces = forces_flat.reshape(self.n_atoms, self.spatial_dims)
        
        return forces


class PhysicsInformedNeuralODE:
    """物理信息神经ODE"""
    
    def __init__(self, 
                 n_atoms: int,
                 masses: jnp.ndarray,
                 learning_rate: float = 1e-3):
        self.n_atoms = n_atoms
        self.masses = masses
        self.learning_rate = learning_rate
        
        # 构建网络
        self._build_networks()
        
        # 优化器
        self.optimizer = optax.adam(learning_rate)
        
    def _build_networks(self):
        """构建神经网络"""
        
        def force_field_fn(t, positions):
            model = ContinuousForceField(self.n_atoms)
            return model(t, positions)
            
        def dynamics_fn(t, state):
            """哈密顿动力学"""
            # 状态向量: [positions, velocities]
            n_coords = self.n_atoms * 3
            positions = state[:n_coords].reshape(self.n_atoms, 3)
            velocities = state[n_coords:].reshape(self.n_atoms, 3)
            
            # 计算力
            forces = force_field_fn(t, positions)
            
            # 牛顿第二定律: F = ma
            accelerations = forces / self.masses[:, None]
            
            # 返回导数: [velocities, accelerations]
            return jnp.concatenate([
                velocities.flatten(),
                accelerations.flatten()
            ])
            
        # 转换为JAX函数
        self.force_field = hk.without_apply_rng(hk.transform(force_field_fn))
        self.dynamics = hk.without_apply_rng(hk.transform(dynamics_fn))
        
        # 初始化参数
        rng = jax.random.PRNGKey(42)
        dummy_t = 0.0
        dummy_pos = jnp.zeros((self.n_atoms, 3))
        dummy_state = jnp.zeros(self.n_atoms * 6)  # pos + vel
        
        self.force_params = self.force_field.init(rng, dummy_t, dummy_pos)
        self.dynamics_params = self.dynamics.init(rng, dummy_t, dummy_state)
        
        # 合并参数
        self.params = {
            'force_field': self.force_params,
            'dynamics': self.dynamics_params
        }
        
        self.opt_state = self.optimizer.init(self.params)
        
    def simulate_trajectory(self, 
                          initial_state: jnp.ndarray,
                          time_points: jnp.ndarray) -> jnp.ndarray:
        """模拟分子轨迹
        
        Args:
            initial_state: 初始状态 [positions, velocities]
            time_points: 时间点
            
        Returns:
            轨迹 [time_steps, n_atoms * 6]
        """
        
        def ode_func(state, t):
            return self.dynamics.apply(self.params['dynamics'], t, state)
            
        trajectory = odeint(ode_func, initial_state, time_points)
        return trajectory
        
    def compute_energy(self, positions: jnp.ndarray, velocities: jnp.ndarray) -> jnp.ndarray:
        """计算总能量"""
        # 动能
        kinetic = 0.5 * jnp.sum(self.masses[:, None] * velocities**2)
        
        # 势能（通过力场积分获得，这里简化处理）
        potential = self._compute_potential_energy(positions)
        
        return kinetic + potential
        
    def _compute_potential_energy(self, positions: jnp.ndarray) -> jnp.ndarray:
        """计算势能（简化版）"""
        # 简单的Lennard-Jones势能
        energy = 0.0
        for i in range(self.n_atoms):
            for j in range(i + 1, self.n_atoms):
                r = jnp.linalg.norm(positions[i] - positions[j])
                # 避免除零
                r = jnp.maximum(r, 0.1)
                energy += 4 * ((1/r)**12 - (1/r)**6)
        return energy
        
    def loss_function(self, params: Dict, 
                     trajectory_data: jnp.ndarray,
                     time_points: jnp.ndarray) -> jnp.ndarray:
        """损失函数"""
        # 更新参数
        old_params = self.params
        self.params = params
        
        # 预测轨迹
        initial_state = trajectory_data[0]
        predicted_trajectory = self.simulate_trajectory(initial_state, time_points)
        
        # 恢复参数
        self.params = old_params
        
        # 轨迹损失
        trajectory_loss = jnp.mean((predicted_trajectory - trajectory_data)**2)
        
        # 能量守恒损失
        energy_loss = self._energy_conservation_loss(predicted_trajectory, time_points)
        
        # 物理约束损失
        physics_loss = self._physics_constraint_loss(predicted_trajectory)
        
        total_loss = trajectory_loss + 0.1 * energy_loss + 0.05 * physics_loss
        
        return total_loss
        
    def _energy_conservation_loss(self, trajectory: jnp.ndarray, 
                                time_points: jnp.ndarray) -> jnp.ndarray:
        """能量守恒损失"""
        energies = []
        
        for i, t in enumerate(time_points):
            state = trajectory[i]
            n_coords = self.n_atoms * 3
            positions = state[:n_coords].reshape(self.n_atoms, 3)
            velocities = state[n_coords:].reshape(self.n_atoms, 3)
            
            energy = self.compute_energy(positions, velocities)
            energies.append(energy)
            
        energies = jnp.array(energies)
        
        # 能量变化应该很小
        energy_variance = jnp.var(energies)
        return energy_variance
        
    def _physics_constraint_loss(self, trajectory: jnp.ndarray) -> jnp.ndarray:
        """物理约束损失"""
        # 例如：原子间距离不应该太小
        min_distance_loss = 0.0
        
        for i in range(len(trajectory)):
            state = trajectory[i]
            n_coords = self.n_atoms * 3
            positions = state[:n_coords].reshape(self.n_atoms, 3)
            
            for atom_i in range(self.n_atoms):
                for atom_j in range(atom_i + 1, self.n_atoms):
                    distance = jnp.linalg.norm(positions[atom_i] - positions[atom_j])
                    # 惩罚过小的距离
                    min_distance_loss += jnp.maximum(0, 0.5 - distance)**2
                    
        return min_distance_loss / len(trajectory)
        
    def train(self, 
              trajectory_data: jnp.ndarray,
              time_points: jnp.ndarray,
              n_epochs: int = 1000) -> Dict:
        """训练模型"""
        
        @jit
        def update_step(params, opt_state, trajectory_data, time_points):
            loss, grads = jax.value_and_grad(self.loss_function)(
                params, trajectory_data, time_points
            )
            updates, opt_state = self.optimizer.update(grads, opt_state)
            params = optax.apply_updates(params, updates)
            return params, opt_state, loss
            
        losses = []
        
        for epoch in range(n_epochs):
            self.params, self.opt_state, loss = update_step(
                self.params, self.opt_state, trajectory_data, time_points
            )
            
            losses.append(float(loss))
            
            if epoch % 100 == 0:
                logger.info(f"Epoch {epoch}, Loss: {loss:.6f}")
                
        return {
            'final_loss': losses[-1],
            'loss_history': losses,
            'trained_params': self.params
        }


class AdjointMethod:
    """伴随方法梯度传播"""
    
    def __init__(self, ode_func: Callable):
        self.ode_func = ode_func
        
    def solve_adjoint(self, 
                     y0: jnp.ndarray,
                     t_span: jnp.ndarray,
                     loss_grad: jnp.ndarray) -> jnp.ndarray:
        """求解伴随方程"""
        
        def augmented_dynamics(state, t):
            """增广动力学系统"""
            y_size = y0.shape[0]
            y = state[:y_size]
            adj_y = state[y_size:2*y_size]
            
            # 原始ODE
            dydt = self.ode_func(y, t)
            
            # 伴随ODE
            vjp_fn = jax.vjp(lambda y: self.ode_func(y, t), y)[1]
            dadj_dt = -vjp_fn(adj_y)[0]
            
            return jnp.concatenate([dydt, dadj_dt])
            
        # 初始条件：原始状态 + 伴随状态
        initial_adj = jnp.zeros_like(y0)
        augmented_y0 = jnp.concatenate([y0, initial_adj])
        
        # 求解增广系统
        solution = odeint(augmented_dynamics, augmented_y0, t_span)
        
        return solution
        
    def compute_parameter_gradients(self, 
                                  params: Dict,
                                  y0: jnp.ndarray,
                                  t_span: jnp.ndarray,
                                  loss_grad: jnp.ndarray) -> Dict:
        """计算参数梯度"""
        
        # 使用伴随方法计算梯度
        def param_ode_func(y, t):
            return self.ode_func(y, t, params)
            
        # 计算VJP
        _, vjp_fn = jax.vjp(
            lambda p: odeint(lambda y, t: param_ode_func(y, t), y0, t_span),
            params
        )
        
        param_grads = vjp_fn(loss_grad)[0]
        
        return param_grads


# 使用示例
def create_molecular_dynamics_model():
    """创建分子动力学模型示例"""
    
    # 系统参数
    n_atoms = 4
    masses = jnp.array([12.0, 1.0, 1.0, 1.0])  # C, H, H, H (甲烷)
    
    # 创建模型
    model = PhysicsInformedNeuralODE(n_atoms, masses)
    
    # 生成模拟数据
    rng = jax.random.PRNGKey(123)
    initial_positions = jax.random.normal(rng, (n_atoms, 3)) * 0.1
    initial_velocities = jax.random.normal(rng, (n_atoms, 3)) * 0.01
    
    initial_state = jnp.concatenate([
        initial_positions.flatten(),
        initial_velocities.flatten()
    ])
    
    time_points = jnp.linspace(0, 1.0, 100)
    
    # 生成参考轨迹（这里用简单的谐振子模拟）
    reference_trajectory = jnp.array([
        initial_state * jnp.cos(t) for t in time_points
    ])
    
    return model, reference_trajectory, time_points
