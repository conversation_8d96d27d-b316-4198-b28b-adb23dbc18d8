#!/usr/bin/env python3
"""
ReaxFFOpt 使用示例
"""

def example_basic_usage():
    """基础使用示例"""
    print("=== ReaxFFOpt 基础使用示例 ===")
    
    # 1. 启动程序
    print("1. 启动程序: python start.py")
    
    # 2. 导入数据集
    print("2. 导入数据集:")
    print("   - 点击 文件 -> 导入 -> 导入数据集文件夹")
    print("   - 选择 Datasets 文件夹")
    print("   - 选择要处理的数据集（推荐先选cobalt）")
    
    # 3. 开始优化
    print("3. 开始优化:")
    print("   - 检查参数表格中的参数")
    print("   - 选择要优化的参数（勾选优化列）")
    print("   - 点击开始优化按钮")
    
    # 4. 查看结果
    print("4. 查看结果:")
    print("   - 在可视化面板中观察优化进度")
    print("   - 查看参数空间搜索路径")
    print("   - 分析AI洞察建议")

def example_advanced_usage():
    """高级使用示例"""
    print("\n=== ReaxFFOpt 高级使用示例 ===")
    
    print("1. AI功能使用:")
    print("   - 点击 🤖 AI分析 按钮获取智能建议")
    print("   - 查看AI洞察选项卡的分析结果")
    
    print("2. 多目标优化:")
    print("   - 选择优化方法为多目标优化")
    print("   - 查看帕累托前沿结果")
    
    print("3. 参数敏感性分析:")
    print("   - 运行敏感性分析")
    print("   - 根据敏感性结果选择关键参数")

if __name__ == "__main__":
    example_basic_usage()
    example_advanced_usage()
