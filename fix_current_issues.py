#!/usr/bin/env python3
"""
修复当前ReaxFFOpt运行问题的脚本
"""

import os
import sys

def check_datasets():
    """检查数据集目录"""
    print("🔍 检查数据集...")
    
    datasets_dir = "Datasets"
    if not os.path.exists(datasets_dir):
        print(f"❌ 数据集目录不存在: {datasets_dir}")
        return False
    
    # 检查cobalt数据集
    cobalt_dir = os.path.join(datasets_dir, "cobalt")
    if not os.path.exists(cobalt_dir):
        print(f"❌ cobalt数据集不存在: {cobalt_dir}")
        return False
    
    print(f"✅ 找到cobalt数据集: {cobalt_dir}")
    
    # 列出cobalt目录内容
    try:
        files = os.listdir(cobalt_dir)
        print(f"📁 cobalt目录包含: {files}")
        
        # 检查关键文件
        required_files = ['geo', 'ffield_lit', 'trainset.in', 'params']
        found_files = []
        missing_files = []
        
        for req_file in required_files:
            if req_file in files:
                found_files.append(req_file)
            else:
                missing_files.append(req_file)
        
        print(f"✅ 找到文件: {found_files}")
        if missing_files:
            print(f"⚠️  缺少文件: {missing_files}")
        
        return len(found_files) > 0
        
    except Exception as e:
        print(f"❌ 读取cobalt目录失败: {e}")
        return False

def create_minimal_ai_modules():
    """创建最小化的AI模块以避免导入错误"""
    print("🔧 创建最小化AI模块...")
    
    # 创建ml目录下的占位符模块
    ml_dir = "ml"
    if not os.path.exists(ml_dir):
        os.makedirs(ml_dir)
    
    # 创建__init__.py
    init_file = os.path.join(ml_dir, "__init__.py")
    if not os.path.exists(init_file):
        with open(init_file, "w") as f:
            f.write('"""ML模块初始化"""')
    
    # 创建简化的multimodal_llm.py占位符
    multimodal_file = os.path.join(ml_dir, "multimodal_llm_simple.py")
    with open(multimodal_file, "w", encoding="utf-8") as f:
        f.write('''"""
简化的多模态LLM模块 - 用于避免导入错误
"""

class ReaxGPT:
    """简化的ReaxGPT类"""
    def __init__(self):
        self.available = False
        print("ReaxGPT (简化版) 已初始化")
    
    def chat_interface(self, user_input, context=None):
        return f"简化版回复: 关于'{user_input}'的问题，请安装完整的AI依赖包以获得完整功能。"

class ReactionVideo3DGenerator:
    """简化的3D视频生成器"""
    def __init__(self):
        self.available = False
    
    def generate_reaction_video(self, reaction_path, fps=30, duration=10.0):
        print("简化版: 3D视频生成功能需要完整的AI依赖包")
        return None

class Molecule3DRenderer:
    """简化的3D分子渲染器"""
    def __init__(self):
        self.available = False
    
    def render(self, config, size=(512, 512)):
        print("简化版: 3D分子渲染功能需要完整的AI依赖包")
        import numpy as np
        return np.ones((size[1], size[0], 3), dtype=np.uint8) * 255
''')
    
    print("✅ 已创建简化AI模块")

def update_visualization_panel():
    """更新可视化面板以使用简化的AI模块"""
    print("🔧 更新可视化面板...")
    
    viz_file = "gui/visualization_panel.py"
    if not os.path.exists(viz_file):
        print(f"❌ 可视化面板文件不存在: {viz_file}")
        return
    
    # 读取文件
    with open(viz_file, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 替换导入语句
    old_import = """# AI功能导入
try:
    from ml.multimodal_llm import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
    from ml.nn_enhanced_forcefield import PhysicsInformedNN
    from jaxreaxff_2.visualization.molecular_viewer import MolecularTrajectoryViewer
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("Warning: AI modules not available. Basic visualization only.")"""
    
    new_import = """# AI功能导入 (简化版)
try:
    from ml.multimodal_llm import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
    from ml.nn_enhanced_forcefield import PhysicsInformedNN
    from jaxreaxff_2.visualization.molecular_viewer import MolecularTrajectoryViewer
    AI_AVAILABLE = True
except ImportError:
    try:
        from ml.multimodal_llm_simple import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
        AI_AVAILABLE = True
        print("使用简化版AI模块")
    except ImportError:
        AI_AVAILABLE = False
        print("Warning: AI modules not available. Basic visualization only.")"""
    
    if old_import in content:
        content = content.replace(old_import, new_import)
        
        # 写回文件
        with open(viz_file, "w", encoding="utf-8") as f:
            f.write(content)
        
        print("✅ 已更新可视化面板")
    else:
        print("⚠️  可视化面板导入语句未找到，可能已经更新")

def create_sample_dataset():
    """创建示例数据集文件"""
    print("📝 创建示例数据集文件...")
    
    cobalt_dir = "Datasets/cobalt"
    if not os.path.exists(cobalt_dir):
        print(f"❌ cobalt目录不存在: {cobalt_dir}")
        return
    
    # 创建简单的geo文件
    geo_file = os.path.join(cobalt_dir, "geo")
    if not os.path.exists(geo_file):
        with open(geo_file, "w") as f:
            f.write("""BIOGRF 200
DESCRP Sample cobalt structure
REMARK Generated for ReaxFFOpt testing
FORCEFIELD REAX
FORMAT ATOM   (a6,1x,i5,1x,a5,1x,a3,1x,a1,1x,a5,3f10.5,1x,a5,i3,i2,1x,f8.5)
HETATM    1 Co1   Co  A     1      0.00000   0.00000   0.00000 Co    0  0  0.00000
HETATM    2 O1    O   A     1      2.00000   0.00000   0.00000 O     0  0  0.00000
HETATM    3 O2    O   A     1      0.00000   2.00000   0.00000 O     0  0  0.00000
HETATM    4 O3    O   A     1      0.00000   0.00000   2.00000 O     0  0  0.00000
END
""")
        print("✅ 已创建geo文件")
    
    # 创建简单的trainset.in文件
    trainset_file = os.path.join(cobalt_dir, "trainset.in")
    if not os.path.exists(trainset_file):
        with open(trainset_file, "w") as f:
            f.write("""# Sample training set for cobalt
ENERGY
1 -100.0
ENDMOL
""")
        print("✅ 已创建trainset.in文件")

def main():
    """主函数"""
    print("🚀 ReaxFFOpt 问题修复脚本")
    print("=" * 50)
    
    # 1. 检查数据集
    print("\n1️⃣ 检查数据集...")
    if not check_datasets():
        create_sample_dataset()
    
    # 2. 创建简化AI模块
    print("\n2️⃣ 创建简化AI模块...")
    create_minimal_ai_modules()
    
    # 3. 更新可视化面板
    print("\n3️⃣ 更新可视化面板...")
    update_visualization_panel()
    
    # 4. 检查Python环境
    print("\n4️⃣ 检查Python环境...")
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("⚠️  警告: Python版本过低，建议升级到3.8+")
    
    # 5. 检查关键包
    print("\n5️⃣ 检查关键包...")
    critical_packages = ["numpy", "matplotlib", "PyQt5"]
    missing_packages = []
    
    for package in critical_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少关键包: {missing_packages}")
        print("请运行: python install_dependencies.py")
    
    print("\n🎉 修复完成！")
    print("\n💡 下一步:")
    print("1. 如果仍有依赖问题，运行: python install_dependencies.py")
    print("2. 重新启动程序: python main.py")
    print("3. 在GUI中选择 Datasets/cobalt 文件夹")

if __name__ == "__main__":
    main()
