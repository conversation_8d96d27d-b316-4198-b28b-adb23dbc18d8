#!/usr/bin/env python3
"""
测试参数解析功能
"""

import os
import sys
from data.data_handler import DatasetHandler

def test_params_parsing():
    """测试参数文件解析"""
    print("🧪 测试参数文件解析功能")
    print("=" * 50)

    # 创建数据处理器
    handler = DatasetHandler()

    # 测试各个数据集
    datasets_dir = "Datasets"
    test_datasets = ["HNO3", "disulfide", "cobalt"]

    for dataset in test_datasets:
        dataset_path = os.path.join(datasets_dir, dataset)
        if not os.path.exists(dataset_path):
            print(f"❌ 数据集不存在: {dataset}")
            continue

        print(f"\n📁 测试数据集: {dataset}")
        print("-" * 30)

        # 查找params文件
        params_file = None
        for root, dirs, files in os.walk(dataset_path):
            if 'params' in files:
                params_file = os.path.join(root, 'params')
                break

        if not params_file:
            print(f"❌ 未找到params文件")
            continue

        print(f"📄 params文件: {params_file}")

        # 测试解析
        try:
            params = handler._parse_params(params_file)
            if params:
                print(f"✅ 解析成功，找到 {len(params)} 个参数:")
                for i, (name, data) in enumerate(params.items()):
                    if i < 5:  # 只显示前5个参数
                        print(f"  - {name}: {data['value']:.4f} [{data['min']:.4f}, {data['max']:.4f}]")
                    elif i == 5:
                        print(f"  ... 还有 {len(params) - 5} 个参数")
                        break
            else:
                print(f"❌ 解析失败，未找到参数")

        except Exception as e:
            print(f"❌ 解析出错: {e}")

def test_full_dataset_processing():
    """测试完整数据集处理"""
    print("\n\n🔧 测试完整数据集处理")
    print("=" * 50)

    handler = DatasetHandler()

    # 测试HNO3数据集
    dataset_path = os.path.join("Datasets", "HNO3")
    if os.path.exists(dataset_path):
        print(f"📁 处理数据集: HNO3")

        try:
            result = handler.process_folder(dataset_path)
            print(f"✅ 处理完成:")
            print(f"  - 结构数: {result['structures']}")
            print(f"  - 能量数: {result['energies']}")
            print(f"  - 力数据: {result['forces']}")
            print(f"  - 训练集: {result['training_sets']}")
            print(f"  - 参数数: {result['parameters']}")

            # 检查参数详情
            all_params = handler.get_all_parameters()
            if all_params:
                print(f"\n📊 参数详情:")
                for dataset_name, params in all_params.items():
                    print(f"  数据集 {dataset_name}: {len(params)} 个参数")

        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()

def test_data_clearing():
    """测试数据清除功能"""
    print("\n\n🧹 测试数据清除功能")
    print("=" * 50)

    handler = DatasetHandler()

    # 先加载一些数据
    dataset_path = os.path.join("Datasets", "HNO3")
    if os.path.exists(dataset_path):
        print("1. 加载数据...")
        result1 = handler.process_folder(dataset_path)
        print(f"   参数数: {result1['parameters']}")

        # 再加载另一个数据集
        dataset_path2 = os.path.join("Datasets", "cobalt")
        if os.path.exists(dataset_path2):
            print("2. 加载另一个数据集...")
            result2 = handler.process_folder(dataset_path2)
            print(f"   参数数: {result2['parameters']}")

            # 检查是否清除了之前的数据
            all_params = handler.get_all_parameters()
            print(f"3. 检查数据清除:")
            print(f"   当前参数数据集数量: {len(all_params)}")
            for dataset_name in all_params.keys():
                print(f"   - {dataset_name}")

            if len(all_params) == 1 and 'cobalt' in all_params:
                print("✅ 数据清除功能正常工作")
            else:
                print("❌ 数据清除功能可能有问题")

def main():
    """主函数"""
    print("🚀 ReaxFFOpt 参数解析测试")
    print("=" * 60)

    # 1. 测试参数解析
    test_params_parsing()

    # 2. 测试完整处理
    test_full_dataset_processing()

    # 3. 测试数据清除
    test_data_clearing()

    print("\n" + "=" * 60)
    print("🎉 测试完成！")

    print("\n💡 如果看到参数解析成功，说明问题已解决。")
    print("现在可以重新启动ReaxFFOpt并导入数据集。")

if __name__ == "__main__":
    main()
