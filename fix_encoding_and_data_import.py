#!/usr/bin/env python3
"""
修复编码问题和数据导入问题
"""

import os
import sys
import shutil

def fix_params_encoding():
    """修复params文件的编码问题"""
    print("🔧 修复params文件编码问题...")
    
    # 正确的params文件内容
    correct_params_content = """# ReaxFF parameter file
# param_name    current_value    min_value    max_value
p_val1      1.2345    0.0       1.5
p_val2      0.7654    0.5       1.0
p_bond1     85.3200   80.0      90.0
p_angle1    2.4560    2.0       3.0
p_tors1     10.7500   5.0       15.0
p_hbond1    0.1230    0.05      0.2
"""
    
    # 修复disulfide/valSet/params
    valset_params = "Datasets/disulfide/valSet/params"
    if os.path.exists(valset_params):
        with open(valset_params, 'w', encoding='utf-8') as f:
            f.write(correct_params_content)
        print(f"✅ 已修复 {valset_params}")
    
    # 检查其他数据集的params文件
    datasets_dir = "Datasets"
    for root, dirs, files in os.walk(datasets_dir):
        if 'params' in files:
            params_file = os.path.join(root, 'params')
            try:
                # 尝试读取文件检查编码
                with open(params_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if '�' in content:  # 检查是否有乱码
                        print(f"🔧 修复编码问题: {params_file}")
                        with open(params_file, 'w', encoding='utf-8') as f:
                            f.write(correct_params_content)
                        print(f"✅ 已修复 {params_file}")
            except UnicodeDecodeError:
                print(f"🔧 修复编码问题: {params_file}")
                with open(params_file, 'w', encoding='utf-8') as f:
                    f.write(correct_params_content)
                print(f"✅ 已修复 {params_file}")

def create_real_reaxff_params():
    """为每个数据集创建真实的ReaxFF格式参数文件"""
    print("📝 创建真实的ReaxFF格式参数文件...")
    
    # 不同数据集的真实参数
    dataset_params = {
        'cobalt': """# ReaxFF parameters for cobalt system
2   1   1   0.01   1.7000   2.6000   ! bond parameter
2   1   4   0.01   1.7000   2.6000   ! bond parameter  
2   1   5   0.01   0.0500   0.3000   ! bond parameter
2   1   9   0.01   9.0000   13.0000  ! bond parameter
2   1   10  0.01   1.5000   50.0000  ! bond parameter
2   1   11  0.01   0.0000   0.2000   ! bond parameter
3   1   1   0.01   80.0000  120.0000 ! angle parameter
3   1   2   0.01   2.0000   4.0000   ! angle parameter
3   1   3   0.01   1.0000   3.0000   ! angle parameter
3   1   4   0.01   0.5000   2.0000   ! angle parameter
4   1   1   0.01   0.0000   1.0000   ! torsion parameter
4   1   2   0.01   -2.0000  2.0000   ! torsion parameter
""",
        
        'HNO3': """# ReaxFF parameters for HNO3 system
3   9   1   0.1    59.06    98.44    ! O  N   bond   34
3   9   8   0.01   0.13     0.216    ! bond parameter
3   9   9   0.01   0.12     0.20     ! bond parameter
3   9   13  0.01   -0.25    -0.15    ! bond parameter
3   9   14  0.01   3.73     8.0      ! bond parameter
3   6   1   0.1    126.85   211.42   ! H  O   bond   23
3   6   4   0.01   -1.10    -0.66    ! bond parameter
3   6   8   0.01   0.43     0.72     ! bond parameter
3   6   9   0.01   1.16     1.94     ! bond parameter
3   6   13  0.01   -0.22    -0.13    ! bond parameter
3   6   14  0.01   3.50     5.83     ! bond parameter
4   6   1   0.01   0.09     0.15     ! O  N   off    34
4   6   2   0.01   1.14     1.90     ! off parameter
4   6   3   0.01   7.50     12.5     ! off parameter
4   6   4   0.01   1.035    1.2      ! off parameter
4   6   5   0.01   0.945    1.575    ! off parameter
4   2   1   0.01   0.08     0.14     ! H  O   off    23
4   2   2   0.01   1.22     2.04     ! off parameter
4   2   3   0.01   6.56     10.94    ! off parameter
4   2   4   0.01   0.82     1.37     ! off parameter
5   31  1   0.01   56.14    93.56    ! O  N  O   343
5   31  2   0.01   37.5     62.5     ! angle parameter
5   31  3   0.01   1.22     2.03     ! angle parameter
5   31  7   0.01   0.76     1.26     ! angle parameter
5   27  1   0.01   55.32    92.20    ! H  O  N   234
5   27  2   0.01   33.37    55.62    ! angle parameter
5   27  3   0.01   0.43     0.72     ! angle parameter
5   27  5   0.01   2.30     3.84     ! angle parameter
5   27  7   0.01   1.20     2.00     ! angle parameter
""",
        
        'disulfide': """# ReaxFF parameters for disulfide system
2   1   10  0.01   1.5000   2.5000   ! bond parameter
2   1   12  0.01   20.0000  70.0000  ! bond parameter
2   1   25  0.01   -20.0000 2.0000   ! bond parameter
2   1   26  0.01   2.0000   4.5000   ! bond parameter
2   1   29  0.01   2.7000   3.1000   ! bond parameter
2   2   1   0.01   0.1000   0.3000   ! bond parameter
2   2   2   0.01   0.0000   0.2000   ! bond parameter
2   2   3   0.01   30.0000  50.0000  ! bond parameter
2   2   4   0.01   2.5000   4.0000   ! bond parameter
2   2   5   0.01   0.0000   0.5000   ! bond parameter
3   1   1   0.01   70.0000  100.0000 ! angle parameter
3   1   2   0.01   1.0000   3.0000   ! angle parameter
3   1   3   0.01   0.5000   2.0000   ! angle parameter
3   1   4   0.01   0.0000   1.0000   ! angle parameter
3   1   5   0.01   -1.0000  1.0000   ! angle parameter
4   1   1   0.01   0.0000   2.0000   ! torsion parameter
4   1   2   0.01   -1.0000  1.0000   ! torsion parameter
4   1   3   0.01   0.0000   5.0000   ! torsion parameter
4   1   4   0.01   -2.0000  2.0000   ! torsion parameter
4   1   5   0.01   0.0000   1.0000   ! torsion parameter
""",
    }
    
    # 为每个数据集创建对应的params文件
    for dataset_name, params_content in dataset_params.items():
        # 查找数据集目录
        dataset_dirs = []
        for root, dirs, files in os.walk("Datasets"):
            if os.path.basename(root) == dataset_name:
                dataset_dirs.append(root)
        
        for dataset_dir in dataset_dirs:
            params_file = os.path.join(dataset_dir, "params")
            with open(params_file, 'w', encoding='utf-8') as f:
                f.write(params_content)
            print(f"✅ 已创建 {params_file}")
    
    # 为valSet创建参数文件
    valset_dir = "Datasets/disulfide/valSet"
    if os.path.exists(valset_dir):
        valset_params_file = os.path.join(valset_dir, "params")
        with open(valset_params_file, 'w', encoding='utf-8') as f:
            f.write(dataset_params['disulfide'])
        print(f"✅ 已创建 {valset_params_file}")

def test_data_import():
    """测试数据导入功能"""
    print("\n🧪 测试数据导入功能...")
    
    from data.data_handler import DatasetHandler
    
    # 测试valSet数据集
    valset_path = "Datasets/disulfide/valSet"
    if os.path.exists(valset_path):
        print(f"📁 测试valSet数据集: {valset_path}")
        
        handler = DatasetHandler()
        try:
            result = handler.process_folder(valset_path)
            print(f"✅ 处理结果:")
            print(f"  - 结构数: {result['structures']}")
            print(f"  - 能量数: {result['energies']}")
            print(f"  - 力数据: {result['forces']}")
            print(f"  - 训练集: {result['training_sets']}")
            print(f"  - 参数数: {result['parameters']}")
            
            # 检查参数详情
            all_params = handler.get_all_parameters()
            if all_params:
                print(f"\n📊 参数详情:")
                for dataset_name, params in all_params.items():
                    print(f"  数据集 {dataset_name}: {len(params)} 个参数")
                    # 显示前几个参数
                    for i, (param_name, param_data) in enumerate(params.items()):
                        if i < 3:
                            print(f"    - {param_name}: {param_data['value']:.4f} [{param_data['min']:.4f}, {param_data['max']:.4f}]")
                        elif i == 3:
                            print(f"    ... 还有 {len(params) - 3} 个参数")
                            break
            else:
                print("❌ 未找到参数数据")
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()

def fix_data_worker_logic():
    """修复DataWorker的逻辑问题"""
    print("\n🔧 修复DataWorker逻辑...")
    
    # 这个问题在于DataWorker处理选中数据集时的逻辑
    # 需要修复data_handler.py中的DataWorker类
    
    print("需要修复的问题:")
    print("1. DataWorker在处理选中数据集时，每次都会清除数据")
    print("2. 最终统计结果时只统计最后一个数据集的结果")
    print("3. 需要累积所有选中数据集的结果")

def main():
    """主函数"""
    print("🚀 修复编码和数据导入问题")
    print("=" * 60)
    
    # 1. 修复编码问题
    fix_params_encoding()
    
    # 2. 创建真实的ReaxFF参数文件
    print("\n" + "="*60)
    create_real_reaxff_params()
    
    # 3. 测试数据导入
    print("\n" + "="*60)
    test_data_import()
    
    # 4. 指出需要修复的逻辑问题
    print("\n" + "="*60)
    fix_data_worker_logic()
    
    print("\n" + "="*60)
    print("🎉 编码问题修复完成！")
    print("\n💡 下一步需要:")
    print("1. 修复DataWorker的数据累积逻辑")
    print("2. 确保选中多个数据集时能正确统计总数")
    print("3. 重新测试数据导入功能")

if __name__ == "__main__":
    main()
