# ReaxFFOpt 使用指南

## 🎉 问题已解决！

经过完整的修复，ReaxFFOpt现在可以正常运行了。以下是详细的使用指南：

## 🚀 启动程序

### 方法1: 使用启动脚本（推荐）
```bash
python start.py
```

### 方法2: 直接启动
```bash
python main.py
```

## 📁 数据集导入

### 1. 打开数据集导入功能
- 点击菜单栏 `文件` -> `导入` -> `导入数据集文件夹`
- 或者使用快捷键 `Ctrl+O`

### 2. 选择数据集文件夹
- 选择 `Datasets` 文件夹
- 程序会自动扫描所有子目录

### 3. 选择要处理的数据集
现在可以识别以下数据集：
- ✅ **cobalt** - 钴材料数据集
- ✅ **disulfide** - 二硫化物数据集  
- ✅ **HNO3** - 硝酸数据集
- ✅ **NO2** - 二氧化氮数据集
- ✅ **RDX** - RDX炸药数据集
- ✅ **silica** - 二氧化硅数据集
- ✅ **tnt1** - TNT炸药数据集

### 4. 确认导入
- 选择一个或多个数据集
- 点击 `确定` 开始导入
- 等待处理完成

## ⚙️ 参数优化

### 1. 查看参数表格
导入数据集后，参数面板会显示：
- **参数名**: 力场参数名称
- **当前值**: 参数的当前数值
- **范围**: 参数的取值范围
- **优化**: 是否参与优化（可勾选）
- **敏感性**: 参数敏感性分析结果

### 2. 配置优化设置
- **优化方法**: PSO（粒子群优化）
- **最大迭代数**: 默认100次
- **收敛容差**: 默认1e-6

### 3. 开始优化
- 点击 `开始优化` 按钮
- 或使用快捷键 `F5`
- 观察进度条和状态信息

### 4. 停止优化
- 点击 `停止优化` 按钮
- 或使用快捷键 `Esc`

## 📊 可视化功能

### 1. 实时监控
- **优化进度**: 实时显示迭代进度
- **能量变化**: 目标函数值变化曲线
- **参数变化**: 参数值随迭代的变化

### 2. 结果分析
- **收敛曲线**: 优化过程的收敛情况
- **参数分布**: 最优参数的分布情况
- **敏感性分析**: 参数对目标函数的影响

## 🔧 高级功能

### 1. 参数敏感性分析
- 点击菜单 `工具` -> `参数敏感性分析`
- 自动计算各参数的敏感性
- 结果显示在参数表格中

### 2. 多目标优化
- 点击菜单 `优化` -> `多目标优化`
- 同时优化能量和力的误差
- 生成帕累托前沿

### 3. AI参数预测
- 点击菜单 `工具` -> `生成式AI参数预测`
- 使用AI模型预测最优参数
- 提供智能初始化

## 💾 保存和导出

### 1. 保存项目
- 点击 `文件` -> `保存项目`
- 保存当前的所有设置和结果

### 2. 导出力场文件
- 点击 `文件` -> `保存` -> `保存力场文件`
- 导出优化后的ReaxFF参数

### 3. 导出结果图表
- 在可视化面板中右键点击图表
- 选择 `保存图像` 导出PNG格式

## 🐛 故障排除

### 问题1: 数据集无法识别
**解决方案**:
- 确保数据集文件夹包含必要文件（geo, params, ffield_lit, trainset.in）
- 检查文件名是否正确（区分大小写）
- 查看控制台输出的详细信息

### 问题2: 优化无法启动
**解决方案**:
- 确保已导入数据集
- 检查参数表格是否有数据
- 至少选择一个参数进行优化

### 问题3: 坐标解析警告
**解决方案**:
- 这是正常现象，程序会跳过无法解析的行
- 不影响主要功能
- 如需详细信息，可查看日志文件

### 问题4: AI功能不可用
**解决方案**:
- 运行 `python install_dependencies.py` 安装AI依赖
- 或使用基础功能（不影响核心优化）

## 📈 性能优化建议

### 1. 数据集选择
- 首次使用建议选择较小的数据集（如cobalt）
- 大型数据集（如disulfide）需要更多计算时间

### 2. 参数设置
- 减少最大迭代数可加快测试速度
- 增加粒子群大小可提高优化质量

### 3. 系统资源
- 确保有足够的内存（推荐8GB+）
- 使用SSD可提高文件读取速度

## 🎯 最佳实践

### 1. 工作流程
1. 导入单个数据集进行测试
2. 运行参数敏感性分析
3. 选择敏感性高的参数进行优化
4. 保存结果并分析

### 2. 参数选择
- 优先优化敏感性高的参数
- 避免同时优化过多参数
- 注意参数的物理意义

### 3. 结果验证
- 对比优化前后的能量误差
- 检查参数值的合理性
- 进行交叉验证

## 📞 技术支持

如果遇到问题，请：
1. 查看控制台输出信息
2. 检查日志文件
3. 参考本使用指南
4. 联系技术支持团队

---

**祝您使用愉快！ReaxFFOpt团队** 🚀
