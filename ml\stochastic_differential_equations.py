"""
随机微分方程(SDE)模块 - 分数阶Langevin动力学和Fokker-Planck逆向求解
"""

import jax
import jax.numpy as jnp
from jax import grad, jit, vmap, random
import numpy as np
from typing import Dict, List, Tuple, Optional, Callable
import scipy.special
from functools import partial
import logging

logger = logging.getLogger(__name__)


class FractionalLangevinSDE:
    """分数阶Langevin随机微分方程"""
    
    def __init__(self, 
                 alpha: float = 0.5,  # 分数阶参数
                 gamma: float = 1.0,  # 摩擦系数
                 temperature: float = 300.0,  # 温度(K)
                 dt: float = 0.001):  # 时间步长
        self.alpha = alpha
        self.gamma = gamma
        self.temperature = temperature
        self.dt = dt
        self.kb = 8.617e-5  # Boltz<PERSON>常数 (eV/K)
        
        # 分数阶核函数的记忆项
        self.memory_kernel = self._compute_memory_kernel()
        
    def _compute_memory_kernel(self, max_steps: int = 10000) -> jnp.ndarray:
        """计算分数阶记忆核函数"""
        t_values = jnp.arange(1, max_steps + 1) * self.dt
        
        # Mittag-Leffler函数的近似
        # K(t) = t^(-alpha) / Gamma(1-alpha)
        kernel = jnp.power(t_values, -self.alpha) / scipy.special.gamma(1 - self.alpha)
        
        return kernel
        
    def simulate_trajectory(self, 
                          initial_position: jnp.ndarray,
                          initial_velocity: jnp.ndarray,
                          force_function: Callable,
                          n_steps: int,
                          rng_key: jax.random.PRNGKey) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """模拟分数阶Langevin轨迹
        
        Args:
            initial_position: 初始位置
            initial_velocity: 初始速度
            force_function: 力函数 F(x)
            n_steps: 模拟步数
            rng_key: 随机数生成器
            
        Returns:
            (positions, velocities) 轨迹
        """
        
        # 初始化轨迹数组
        positions = jnp.zeros((n_steps + 1,) + initial_position.shape)
        velocities = jnp.zeros((n_steps + 1,) + initial_velocity.shape)
        
        positions = positions.at[0].set(initial_position)
        velocities = velocities.at[0].set(initial_velocity)
        
        # 存储历史速度用于记忆项计算
        velocity_history = [initial_velocity]
        
        # 噪声强度
        noise_strength = jnp.sqrt(2 * self.gamma * self.kb * self.temperature)
        
        for step in range(n_steps):
            rng_key, subkey = random.split(rng_key)
            
            # 当前状态
            x_current = positions[step]
            v_current = velocities[step]
            
            # 计算力
            force = force_function(x_current)
            
            # 计算记忆项（分数阶导数）
            memory_term = self._compute_memory_term(velocity_history, step)
            
            # 生成随机噪声
            noise = random.normal(subkey, v_current.shape) * noise_strength
            
            # 分数阶Langevin方程：
            # dv/dt = F(x)/m - gamma * D^alpha v + sqrt(2*gamma*kT) * xi(t)
            dv_dt = force - self.gamma * memory_term + noise
            
            # 更新速度和位置
            v_new = v_current + dv_dt * self.dt
            x_new = x_current + v_new * self.dt
            
            positions = positions.at[step + 1].set(x_new)
            velocities = velocities.at[step + 1].set(v_new)
            
            # 更新速度历史
            velocity_history.append(v_new)
            
            # 限制历史长度以节省内存
            if len(velocity_history) > len(self.memory_kernel):
                velocity_history.pop(0)
                
        return positions, velocities
        
    def _compute_memory_term(self, velocity_history: List[jnp.ndarray], 
                           current_step: int) -> jnp.ndarray:
        """计算分数阶记忆项"""
        if current_step == 0:
            return jnp.zeros_like(velocity_history[0])
            
        memory_term = jnp.zeros_like(velocity_history[0])
        
        # 卷积积分的离散化
        for i, v_past in enumerate(velocity_history[:-1]):
            if i < len(self.memory_kernel):
                memory_term += self.memory_kernel[i] * v_past * self.dt
                
        return memory_term


class FokkerPlanckSolver:
    """Fokker-Planck方程求解器"""
    
    def __init__(self, 
                 x_min: float = -5.0,
                 x_max: float = 5.0,
                 nx: int = 256,
                 dt: float = 0.001):
        self.x_min = x_min
        self.x_max = x_max
        self.nx = nx
        self.dt = dt
        
        # 空间网格
        self.x = jnp.linspace(x_min, x_max, nx)
        self.dx = (x_max - x_min) / (nx - 1)
        
    def solve_forward(self, 
                     initial_distribution: jnp.ndarray,
                     drift_function: Callable,
                     diffusion_function: Callable,
                     n_steps: int) -> jnp.ndarray:
        """求解前向Fokker-Planck方程
        
        ∂p/∂t = -∂/∂x[μ(x)p] + (1/2)∂²/∂x²[σ²(x)p]
        
        Args:
            initial_distribution: 初始概率分布
            drift_function: 漂移函数 μ(x)
            diffusion_function: 扩散函数 σ(x)
            n_steps: 时间步数
            
        Returns:
            时间演化的概率分布
        """
        
        # 初始化解数组
        solution = jnp.zeros((n_steps + 1, self.nx))
        solution = solution.at[0].set(initial_distribution)
        
        # 计算漂移和扩散系数
        drift = drift_function(self.x)
        diffusion_squared = diffusion_function(self.x)**2
        
        for step in range(n_steps):
            p_current = solution[step]
            
            # 计算空间导数（有限差分）
            dp_dx = self._compute_first_derivative(p_current)
            d2p_dx2 = self._compute_second_derivative(p_current)
            
            # Fokker-Planck方程
            dp_dt = (-self._compute_first_derivative(drift * p_current) + 
                    0.5 * self._compute_second_derivative(diffusion_squared * p_current))
            
            # 时间步进
            p_new = p_current + dp_dt * self.dt
            
            # 确保概率非负且归一化
            p_new = jnp.maximum(p_new, 0)
            p_new = p_new / (jnp.sum(p_new) * self.dx)
            
            solution = solution.at[step + 1].set(p_new)
            
        return solution
        
    def solve_backward(self, 
                      final_distribution: jnp.ndarray,
                      drift_function: Callable,
                      diffusion_function: Callable,
                      n_steps: int) -> jnp.ndarray:
        """求解后向Fokker-Planck方程（逆向求解）
        
        Args:
            final_distribution: 最终概率分布
            drift_function: 漂移函数
            diffusion_function: 扩散函数
            n_steps: 时间步数
            
        Returns:
            逆向时间演化的概率分布
        """
        
        # 后向方程：∂p/∂t = +∂/∂x[μ(x)p] - (1/2)∂²/∂x²[σ²(x)p]
        # （时间反向）
        
        solution = jnp.zeros((n_steps + 1, self.nx))
        solution = solution.at[0].set(final_distribution)
        
        drift = drift_function(self.x)
        diffusion_squared = diffusion_function(self.x)**2
        
        for step in range(n_steps):
            p_current = solution[step]
            
            # 后向方程（符号相反）
            dp_dt = (+self._compute_first_derivative(drift * p_current) - 
                    0.5 * self._compute_second_derivative(diffusion_squared * p_current))
            
            p_new = p_current + dp_dt * self.dt
            p_new = jnp.maximum(p_new, 0)
            p_new = p_new / (jnp.sum(p_new) * self.dx)
            
            solution = solution.at[step + 1].set(p_new)
            
        return solution
        
    def _compute_first_derivative(self, f: jnp.ndarray) -> jnp.ndarray:
        """计算一阶导数（中心差分）"""
        df_dx = jnp.zeros_like(f)
        
        # 中心差分
        df_dx = df_dx.at[1:-1].set((f[2:] - f[:-2]) / (2 * self.dx))
        
        # 边界条件（前向/后向差分）
        df_dx = df_dx.at[0].set((f[1] - f[0]) / self.dx)
        df_dx = df_dx.at[-1].set((f[-1] - f[-2]) / self.dx)
        
        return df_dx
        
    def _compute_second_derivative(self, f: jnp.ndarray) -> jnp.ndarray:
        """计算二阶导数（中心差分）"""
        d2f_dx2 = jnp.zeros_like(f)
        
        # 中心差分
        d2f_dx2 = d2f_dx2.at[1:-1].set(
            (f[2:] - 2*f[1:-1] + f[:-2]) / (self.dx**2)
        )
        
        # 边界条件（设为0）
        d2f_dx2 = d2f_dx2.at[0].set(0)
        d2f_dx2 = d2f_dx2.at[-1].set(0)
        
        return d2f_dx2


class StochasticReaxFFOptimizer:
    """基于随机微分方程的ReaxFF参数优化器"""
    
    def __init__(self, 
                 parameter_bounds: Dict[str, Tuple[float, float]],
                 alpha: float = 0.8,  # 分数阶参数
                 temperature: float = 300.0):
        self.parameter_bounds = parameter_bounds
        self.alpha = alpha
        self.temperature = temperature
        
        # 参数名称和维度
        self.param_names = list(parameter_bounds.keys())
        self.n_params = len(self.param_names)
        
        # 创建SDE求解器
        self.sde_solver = FractionalLangevinSDE(
            alpha=alpha,
            temperature=temperature
        )
        
    def optimize(self, 
                objective_function: Callable,
                initial_params: Dict[str, float],
                n_steps: int = 10000,
                rng_key: jax.random.PRNGKey = None) -> Dict:
        """使用随机微分方程优化参数
        
        Args:
            objective_function: 目标函数
            initial_params: 初始参数
            n_steps: 优化步数
            rng_key: 随机数生成器
            
        Returns:
            优化结果
        """
        
        if rng_key is None:
            rng_key = jax.random.PRNGKey(42)
            
        # 将参数转换为向量
        param_vector = jnp.array([initial_params[name] for name in self.param_names])
        initial_velocity = jnp.zeros_like(param_vector)
        
        # 定义力函数（负梯度）
        def force_function(params):
            # 将向量转换回参数字典
            param_dict = {name: params[i] for i, name in enumerate(self.param_names)}
            
            # 计算梯度
            grad_fn = jax.grad(lambda p: objective_function(
                {name: p[i] for i, name in enumerate(self.param_names)}
            ))
            gradient = grad_fn(params)
            
            # 返回负梯度作为力
            return -gradient
            
        # 运行SDE模拟
        positions, velocities = self.sde_solver.simulate_trajectory(
            param_vector, initial_velocity, force_function, n_steps, rng_key
        )
        
        # 找到最优参数
        best_idx = 0
        best_value = float('inf')
        
        for i in range(len(positions)):
            param_dict = {name: positions[i][j] for j, name in enumerate(self.param_names)}
            value = objective_function(param_dict)
            
            if value < best_value:
                best_value = value
                best_idx = i
                
        # 构建结果
        best_params = {name: positions[best_idx][i] for i, name in enumerate(self.param_names)}
        
        return {
            'best_params': best_params,
            'best_value': best_value,
            'trajectory': positions,
            'velocity_trajectory': velocities,
            'convergence_history': [
                objective_function({name: positions[i][j] for j, name in enumerate(self.param_names)})
                for i in range(0, len(positions), max(1, len(positions) // 100))
            ]
        }
        
    def estimate_parameter_distribution(self, 
                                      objective_function: Callable,
                                      n_samples: int = 1000) -> Dict:
        """估计参数的概率分布"""
        
        # 使用Fokker-Planck求解器
        fp_solver = FokkerPlanckSolver()
        
        # 为每个参数创建分布估计
        distributions = {}
        
        for param_name in self.param_names:
            # 定义该参数的漂移和扩散函数
            def drift_func(x):
                # 简化：假设漂移与梯度成正比
                return -x  # 向最优值漂移
                
            def diffusion_func(x):
                # 扩散强度
                return jnp.ones_like(x) * 0.1
                
            # 初始均匀分布
            initial_dist = jnp.ones(fp_solver.nx) / fp_solver.nx
            
            # 求解前向方程
            evolution = fp_solver.solve_forward(
                initial_dist, drift_func, diffusion_func, 1000
            )
            
            distributions[param_name] = {
                'x_grid': fp_solver.x,
                'final_distribution': evolution[-1],
                'evolution': evolution
            }
            
        return distributions


# 使用示例
def create_stochastic_optimization_example():
    """创建随机优化示例"""
    
    # 定义参数边界
    param_bounds = {
        'p_val1': (-2.0, 2.0),
        'p_val2': (-1.0, 1.0),
        'p_bond1': (50.0, 150.0)
    }
    
    # 创建优化器
    optimizer = StochasticReaxFFOptimizer(param_bounds, alpha=0.7)
    
    # 定义目标函数（Rosenbrock函数的变形）
    def objective(params):
        x = params['p_val1']
        y = params['p_val2']
        z = params['p_bond1'] / 100.0  # 归一化
        
        return (1 - x)**2 + 100*(y - x**2)**2 + (z - 1)**2
        
    # 初始参数
    initial_params = {
        'p_val1': 0.0,
        'p_val2': 0.0,
        'p_bond1': 100.0
    }
    
    return optimizer, objective, initial_params
